"use strict";

// const _ = require('lodash');
const promise = require('bluebird');
const UserModel = require('../models/UserModel');
const ServicesModel = require('../models/ServicesModel');
const ProducModel = require('../models/ProducModel');
const BookSpaModel = require('../models/BookSpaModel');
const BookRoomModel = require('../models/BookRoomModel');
const BookExaminationModel = require('../models/BookExaminationModel');
const BookClassificationModel = require('../models/BookClassificationModel');
const RequestBuyProductModel = require('../models/RequestBuyProductModel');
const NotificationModel = require('../models/NotificationModel');
const VpnPayModel = require('../models/VpnPayModel');
const CouponModel = require('../models/CouponModel');
const SettingAdminModel = require("../models/SettingAdminModel");
const CouponUserModel = require('../models/CouponUserModel');
const CfVpnPay = require('../config/CfVpnPay');
const {firebaseAdmin} = require("../config/CfFirebaseAdmin");
const sha256 = require('sha256');
const moment = require('moment');
const querystring = require('qs');
const {TypeServices} = require("../models/enum/TypeServices");
const {sortObject} = require("./StringUtils");


exports.checkExistOrder = async function (orderId, bookingType) {

    let order = null;
    switch (Number(bookingType)) {
        case TypeServices.SHOP:
            // 0: Đặt hàng
            return false;
            break;
        case TypeServices.CLINIC:
            // 1: Phòng Khám
            order = await BookExaminationModel.MODEL.getOneBookExaminationByCondition({orderId})
            if (order != null) {
                return true;
            }
            break;
        case TypeServices.HOTEL:
            // 2: Điểm gửi xe
            order = await BookRoomModel.MODEL.getOneBookRoomByCondition({orderId})
            if (order != null) {
                return true;
            }
            break;
        case TypeServices.SPA:
            // 3: Spa
            order = await BookSpaModel.MODEL.getOneBookSpaByCondition({orderId})
            if (order != null) {
                return true;
            }
            break;
        case TypeServices.SHOWROOM:
            // 3: Spa
            order = await BookClassificationModel.MODEL.getOneByCondition({orderId})
            if (order != null) {
                return true;
            }
            break;
    }
    return false;
}

exports.vnpayRequestPayment = async function (req, orderId, userId, price, bankCode, attrs = []) {
    price = Number(price)
    const amount = Number(price * 100).toFixed(0);
    let ipAddr = req.headers['x-forwarded-for'] ||
        req.connection.remoteAddress ||
        req.socket.remoteAddress ||
        req.connection.socket.remoteAddress;

    let settingAd = await SettingAdminModel.MODEL.getSettingAdmin();
    let secretKey = settingAd.vnpHashSecret;
    let vnpTmnCode = settingAd.vnpTmnCode;

    let vnpUrl = settingAd.vnpDevMode ? settingAd.vnpUrlSanbox : settingAd.vnpUrlProd;

    let returnUrl = CfVpnPay['vnp_ReturnUrl'];
    let time = new Date().getTime();
    let vnp_Params = {};
    vnp_Params['vnp_Version'] = '2.1.0';
    vnp_Params['vnp_Command'] = 'pay';
    vnp_Params['vnp_TmnCode'] = vnpTmnCode;
    // vnp_Params['vnp_Merchant'] = ''
    vnp_Params['vnp_Locale'] = 'vn';
    vnp_Params['vnp_CurrCode'] = 'VND';
    vnp_Params['vnp_TxnRef'] = orderId;
    vnp_Params['vnp_OrderInfo'] = CfVpnPay['vnp_OrderInfo'];
    vnp_Params['vnp_OrderType'] = CfVpnPay['vnp_OrderType'];
    vnp_Params['vnp_Amount'] = amount;
    vnp_Params['vnp_ReturnUrl'] = returnUrl;
    vnp_Params['vnp_IpAddr'] = ipAddr;
    vnp_Params['vnp_CreateDate'] = moment().format('YYYYMMDDHHmmss');
    if (bankCode !== null && bankCode !== '') {
        vnp_Params['vnp_BankCode'] = bankCode;
    }

    vnp_Params = sortObject(vnp_Params);

    let signData = querystring.stringify(vnp_Params, {encode: false});
    var crypto = require("crypto");
    var hmac = crypto.createHmac("sha512", secretKey);
    var signed = hmac.update(new Buffer(signData, 'utf-8')).digest("hex");
    vnp_Params['vnp_SecureHash'] = signed;
    vnpUrl += '?' + querystring.stringify(vnp_Params, { encode: false });

    await VpnPayModel.MODEL.addVpnPay({
        userId: userId,
        orderId,
        time,
        amount,
        attrs
    });

    try {
        let db = firebaseAdmin.database()
        // const docRef = db.collection('paymentVnPay').doc(id).collection('data');
        let currentDateString = moment(new Date()).format('DD-MM-YYYY')
        let docRef = db.ref(`payment/${currentDateString}/${orderId}/vnpay_create_order`);
        vnp_Params['orderId'] = orderId
        vnp_Params['vnpUrl'] = vnpUrl
        docRef.set(vnp_Params);
    } catch (e) {
        console.log(e)
    }
    return vnpUrl;
}

exports.orderProducts = async function (req, orderId, shopProducts, phone, province, district, ward, street, location, payment) {
    let mapShopFunc = shopProducts.map((shopData) => {
        return new promise(async resolve => {
            let shopId;
            let products;
            for (let shopIdIn in shopData) {
                shopId = shopIdIn;
                products = shopData[shopIdIn].products;
            }

            products.forEach(p => {
                let realPId = p.productId.split('@')[0];
                ProducModel.MODEL.addCountRevenueProduct(realPId, p.count);
            });

            let shopInfo = await ServicesModel.MODEL.getServicesById(shopId);
            let storeId = shopInfo.userId;
            let couponId = '' // Co dinh
            let request = await RequestBuyProductModel.MODEL.addRequest(orderId, req.user._id, storeId, shopId, products, phone, province, district, ward, street, location, payment, couponId);
            if (request && request._id)
            {
                await NotificationModel.MODEL.addNewNotification(storeId, req.user._id, "User đặt sản phẩm", 0, io, {
                    requestId: request._id,
                    storeId: shopId,
                    orderId: orderId
                });
            }
            return resolve();
        })
    });

    return mapShopFunc;
}

exports.beforeBooking = async function (req, items, bookingType) {

    let error = false;
    items = items || [];
    let rs = items.map((item) => {
        return new promise(async resolve => {

            let storeId = item.storeId ? item.storeId : null;
            let rsData = {
                storeId: storeId,
                itemId: item._id,
                itemName: item.serviceName
            };

            if (storeId) {
                let store = await ServicesModel.MODEL.getServicesById(storeId);
                let timeRequest = item.timeCheckIn;
                let currentTime = new Date().getTime();
                timeRequest = new Date(timeRequest).getTime();

                if (timeRequest < currentTime) {
                    error = true;
                    rsData.err = true;
                    rsData.msg = 'TIME_CHECK_IN_ERROR';
                } else {
                    let timeRequestMoment = moment(timeRequest).format("HH:mm:ss");

                    let storeTimeOpen = store.timeOpen ? store.timeOpen : '';
                    let storeTimeClose = store.timeClose ? store.timeClose : '';

                    if (bookingType === 3) {

                    }

                    if (storeTimeOpen && storeTimeClose && (storeTimeOpen > timeRequestMoment || storeTimeClose < timeRequestMoment)) {
                        error = true;
                        rsData.err = true;
                        rsData.msg = 'OUTSIDE_SERVICE_HOURS';
                    } else {
                        rsData.err = false;
                        rsData.msg = 'TIME_INVALID';
                    }
                }
            }
            return resolve(rsData);
        })
    });
    let result = await promise.all(rs);
    let data = {
        error,
        result
    }

    return data;
}

exports.changeStatusBooking = async function (req, storeId, status, message, bookingType) {

}
// exports.checkLimitDay = async function (coupon, currentTime) {
//     let rsCoupon;
//     let getCouponUser = await CouponUserModel.MODEL.getDataWhere({
//         coupon: coupon.code,
//         userId
//     }, CouponUserModel.MODEL.FIND_ONE(), {createAt: -1});
//
//     if (getCouponUser) {
//         let createAt = Number(getCouponUser.createAt);
//         let timeOneDay = 1 * 24 * 60 * 60 * 1000; // Cố định 1 day : tính theo Timestamp milliseconds
//         if ((currentTime - createAt) < timeOneDay) {
//             rsCoupon = {
//                 msg: 'Bạn đã dùng mã này trong 24 giờ trước, vui lòng sử dụng sau.',
//                 error: true
//             }
//         }
//     }
//     return rsCoupon
// }

exports.calculateCouponShop = async function (userId, code, subTotal, feeShip, storeId) {
    console.log("🚀 ~ storeId:", storeId)
    storeId = storeId || '';
    if (!code) return null;
    code = code?.toLowerCase();

    let discount = 0
    let rsCoupon = {
        msg: '',
        error: false
    }

    let coupon = null

    if (code) {
        console.log('coupon---', code)
        let condition = {
            code,
            status: 1
        }
        // if (storeId) condition.storeId = storeId;

        coupon = await CouponModel.MODEL.getDataWhere(condition, CouponModel.MODEL.FIND_ONE(), {createAt: -1});
        storeId = coupon.storeId;
        console.log("🚀 ~ coupon:", coupon)
        if (coupon) {
                let currentTime = new Date().getTime();
                if (Number(coupon.currentBooking) < Number(coupon.countBooking) && currentTime >= coupon.startTime && currentTime <= coupon.endTime) {
                    //  check code store tu tao
                    if (storeId) {
                        if (Number(coupon.typeCode) == 1 && coupon.storeId == storeId) {
                            let getCouponUser = await CouponUserModel.MODEL.getDataWhere({
                                coupon: coupon.code,
                                userId
                            }, CouponUserModel.MODEL.FIND_ONE(), {createAt: -1});

                            if (getCouponUser) {
                                let createAt = Number(getCouponUser.createAt);
                                let timeOneDay = 1 * 24 * 60 * 60 * 1000; // Cố định 1 day : tính theo Timestamp milliseconds
                                if ((currentTime - createAt) < timeOneDay) {
                                    rsCoupon = {
                                        msg: 'Bạn đã dùng mã này trong 24 giờ trước, vui lòng sử dụng sau.',
                                        error: true
                                    }
                                }
                            }
                            if (coupon.minBillValue > subTotal) {
                                rsCoupon = {
                                    msg: 'Đơn hàng chưa đạt giá trị tối thiểu',
                                    error: true
                                }
                            }
                        } else {
                            rsCoupon = {
                                msg: 'Mã giảm giá không hợp lệ hoặc đã hết hạn',
                                error: true
                            }
                        }
                    } else {
                        if (coupon.typeCode == 1) {
                            rsCoupon = {
                                msg: 'Mã giảm giá chỉ áp dụng cho cửa hàng, không nhập vào mã giám giá chung',
                                error: true
                            }
                        } else {
                            // Kiểm tra coupon limit by day.
                            let getCouponUser = await CouponUserModel.MODEL.getDataWhere({
                                coupon: coupon.code,
                                userId
                            }, CouponUserModel.MODEL.FIND_ONE(), {createAt: -1});

                            if (getCouponUser) {
                                let createAt = Number(getCouponUser.createAt);
                                let timeOneDay = 1 * 24 * 60 * 60 * 1000; // Cố định 1 day : tính theo Timestamp milliseconds
                                if ((currentTime - createAt) < timeOneDay) {
                                    rsCoupon = {
                                        msg: 'Bạn đã dùng mã này trong 24 giờ trước, vui lòng sử dụng sau.',
                                        error: true
                                    }
                                }
                            }
                            if (coupon.minBillValue > subTotal) {
                                rsCoupon = {
                                    msg: 'Đơn hàng chưa đạt giá trị tối thiểu',
                                    error: true
                                }
                            }

                        }

                    }

                    // subTotal = Number(subTotal) + Number(feeShip)

                } else {
                    rsCoupon = {
                        msg: 'Mã giảm giá không hợp lệ hoặc đã hết hạn',
                        error: true
                    }
                }
        } else {
            rsCoupon = {
                msg: 'Mã giảm giá không hợp lệ hoặc đã hết hạn',
                error: true
            }
        }
        // GIAM GIA THEO % type = 1
        if (coupon && !rsCoupon.error) {
            discount = coupon.type === 1 ? (subTotal * Number(coupon.value) || 0) / 100 : Number(coupon.value)
        }
    }

    // const totalPrice = subTotal - (discount > 50000 ? 50000 : discount) //TODO: add rules
    const totalPrice = subTotal - discount //TODO: add rules
    return {
        coupon,
        subTotal,
        discount,
        totalPrice,
        rsCoupon
    }
}

exports.calculateCouponService = async function (userId, code, subTotal, feeShip, storeId) {
    storeId = storeId || '';
    code = code.toLowerCase();

    let discount = 0
    let rsCoupon = {
        msg: '',
        error: false
    }

    let coupon = null

    if (code) {
        console.log('coupon---', code)
        let condition = {
            code,
            status: 1
        }
        // if (storeId) condition.storeId = storeId;

        coupon = await CouponModel.MODEL.getDataWhere(condition, CouponModel.MODEL.FIND_ONE(), {createAt: -1});

        if (coupon) {
                let currentTime = new Date().getTime();
                if (Number(coupon.currentBooking) < Number(coupon.countBooking)
                    && currentTime >= coupon.startTime && currentTime <= coupon.endTime) {

                    // todo check code store tu tao

                    if (storeId) {
                        if (coupon.typeCode == 1 && coupon.storeId == storeId) {
                            let getCouponUser = await CouponUserModel.MODEL.getDataWhere({
                                coupon: coupon.code,
                                userId
                            }, CouponUserModel.MODEL.FIND_ONE(), {createAt: -1});

                            if (getCouponUser) {
                                let createAt = Number(getCouponUser.createAt);
                                let timeOneDay = 1 * 24 * 60 * 60 * 1000; // Cố định 1 day : tính theo Timestamp milliseconds
                                if ((currentTime - createAt) < timeOneDay) {
                                    rsCoupon = {
                                        msg: 'Bạn đã dùng mã này trong 24 giờ trước, vui lòng sử dụng sau.',
                                        error: true
                                    }
                                }
                            }
                            if (coupon.minBillValue > subTotal) {
                                rsCoupon = {
                                    msg: 'Đơn hàng chưa đạt giá trị tối thiểu',
                                    error: true
                                }
                            }
                        } else {
                            rsCoupon = {
                                msg: 'Mã giảm giá không hợp lệ hoặc đã hết hạn',
                                error: true
                            }
                        }
                    } else {
                        if (coupon.typeCode == 1) {
                            rsCoupon = {
                                msg: 'Mã giảm giá không hợp lệ hoặc đã hết hạn',
                                error: true
                            }
                        } else {

                            // Kiểm tra coupon limit by day.
                            let getCouponUser = await CouponUserModel.MODEL.getDataWhere({
                                coupon: coupon.code,
                                userId
                            }, CouponUserModel.MODEL.FIND_ONE(), {createAt: -1});

                            if (getCouponUser) {
                                let createAt = Number(getCouponUser.createAt);
                                let timeOneDay = 1 * 24 * 60 * 60 * 1000; // Cố định 1 day : tính theo Timestamp milliseconds
                                if ((currentTime - createAt) < timeOneDay) {
                                    rsCoupon = {
                                        msg: 'Bạn đã dùng mã này trong 24 giờ trước, vui lòng sử dụng sau.',
                                        error: true
                                    }
                                }
                            }
                            if (coupon.minBillValue > subTotal) {
                                rsCoupon = {
                                    msg: 'Đơn hàng chưa đạt giá trị tối thiểu',
                                    error: true
                                }
                            }
                        }

                    }
                    // subTotal = Number(subTotal) + Number(feeShip)

                } else {
                    rsCoupon = {
                        msg: 'Mã giảm giá không hợp lệ hoặc đã hết hạn',
                        error: true
                    }
                }
        } else {
            rsCoupon = {
                msg: 'Mã giảm giá không hợp lệ hoặc đã hết hạn',
                error: true
            }
        }
        // GIAM GIA THEO % type = 1
        if (coupon && !rsCoupon.error) {
            discount = coupon.type === 1 ? (subTotal * Number(coupon.value) || 0) / 100 : Number(coupon.value)
        }
    }

    // const totalPrice = subTotal - (discount > 50000 ? 50000 : discount) //TODO: add rules
    const totalPrice = subTotal - discount //TODO: add rules
    return {
        coupon,
        subTotal,
        discount,
        totalPrice,
        rsCoupon
    }
}
