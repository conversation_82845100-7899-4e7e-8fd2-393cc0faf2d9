'use strict';

const ChildRouter = require('../config/router/ChildRouting');
const UserModel = require('../models/UserModel');
const CategoryModel = require('../models/CategoryModel');
const ProducModel = require('../models/ProducModel');
const ServiceOfSpaModel = require('../models/ServiceOfSpaModel');
const ServiceOfClinicModel = require('../models/ServiceOfClinicModel');
const FileUtils = require('../utils/FileUtils');
const NumberUtils = require('../utils/NumberUtils');
const ServicesModel = require('../models/ServicesModel');
const MessageModel = require('../models/MessageModel');
const BookExaminationModel = require('../models/BookExaminationModel');
const BookRoomModel = require('../models/BookRoomModel');
const BookSpaModel = require('../models/BookSpaModel');
const RequestBuyProductModel = require('../models/RequestBuyProductModel');
const StringUtils = require('../utils/StringUtils');
const { sendHasMessageNotification } = require('../utils/Notification');
const NotificationModel = require('../models/NotificationModel');
const htmlspecialchars = require('htmlspecialchars');
const SettingAdminModel = require('../models/SettingAdminModel');
const FundLogModel = require('../models/FundLogModel');
const promise = require('bluebird');
const RechargeModel = require('../models/RechargeModel');
const HuongDanNapTienModel = require('../models/HuongDanNapTienModel');
const UserSettingModel = require('../models/UserSettingModel');
const AccountBankModel = require('../models/AccountBankModel');
const CouponModel = require('../models/CouponModel');
const WalletOnlineModel = require('../models/WalletOnlineModel');
const PayingWalletLogModel = require('../models/PayingWalletLogModel');
const BranchModel = require('../models/BranchModel');
const RoomOfHotelModel = require('../models/RoomOfHotelModel');
const { ProductOrderStatus } = require('../models/enum/ProductOrderStatus');
const APP = require('../../app');
const GHTK = require('../../www/utils/GiaoHang/ghtk');
const XLSX = require('xlsx');
const ServiceUpdateData = require('../utils/ServiceUpdateData');
const PointUtil = require('../utils/PointUtil');
const validate = require('validate.js');
const ClassificationOfBrandCol = require('../database/ClassificationOfBrandCol');
const ClassificationOfBrandModel = require('../models/ClassificationOfBrandModel');
const BookClassificationModel = require('../models/BookClassificationModel');
const {TypeServices} = require("../models/enum/TypeServices");

module.exports = class Auth extends ChildRouter {
  constructor() {
    super('/store');
  }

  registerRouting(io) {
    let dateTimes = [];
    for (let i = 0; i <= 23; i++) {
      let time = '';
      if (i < 10) {
        time += `0${i} : `;
      } else {
        time += `${i} : `;
      }
      dateTimes.push(time + '00');
      dateTimes.push(time + '30');
    }
    return {
      '/': {
        config: {
          auth: [this.roles.store],
          get: 'view',
        },

        methods: {
          get: [
            async function (req, res) {
              return ChildRouter.redirect(res, '/thong-tin-tai-khoan.html');
            },
          ],
        },
      },
      '/mng-products.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/quan-ly-san-pham.ejs',
          get: 'view',
          title: 'Quản lý sản phẩm',
        },

        methods: {
          get: [
            async function (req, res) {
              let categories = await CategoryModel.MODEL.getAllCategory();
              let objCategories = {};
              categories.forEach((item) => {
                objCategories[item._id] = item.name;
              });
              return ChildRouter.renderOrResponse(req, res, { categories });
            },
          ],
        },
      },
      // Room
      '/mng-parking.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/room/room-list.ejs',
          get: 'view',
          title: 'Quản lý điểm gửi xe',
        },
        methods: {
          get: [
            async function (req, res) {
              let categories = await RoomOfHotelModel.MODEL.getAllRooms();
              let objCategories = {};
              categories.forEach((item) => {
                objCategories[item._id] = item.name;
              });
              return ChildRouter.renderOrResponse(req, res, { categories });
            },
          ],
        },
      },
      '/update-status-parking/:id.html': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              let product = await RoomOfHotelModel.MODEL.getDataById(
                  req.params.id.trim()
              );
              if (
                  !product ||
                  product.userId != req.user._id
              ) {
                return ChildRouter.responseError('Yêu cầu không hợp lệ', res);
              }

              await RoomOfHotelModel.MODEL.updateById(
                  req.params.id.trim(),
                  { status: Number(req.query.status) },
              );
              return ChildRouter.responseSuccess('Thành công', res);
            },
          ],
        },
      },
      '/get-room.html/:status': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },
        methods: {
          get: [
            async function (req, res) {
              let { status } = req.params;
              let products =
                await RoomOfHotelModel.MODEL.getRoomByConditionForStore(
                  {
                    userId: req.user._id,
                    status: Number(status),
                  },
                  { createAt: -1 }
                );
              return ChildRouter.responseSuccess('Thành công', res, {
                products,
              });
            },
          ],
        },
      },
      '/create-parking.html': {
        config: {
          auth: [this.roles.all],
          view: 'public/index.ejs',
          inc: 'inc/store/room/room-add.ejs',
          get: 'view',
          title: 'Thêm mới Iparking',
          upload: [
            { name: 'thumbail', maxCount: 1 },
            { name: 'pictures', maxCount: 15 },
          ],
          post: 'json',
          validate: {
            body: {
              method: ['post'],
              validate: [
                {
                  key: 'name',
                  type: this.dataType.string,
                  name: 'Tên phòng',
                  min: 5,
                  max: 200,
                },
                {
                  key: 'storeId',
                  type: this.dataType.string,
                  name: 'Thương hiệu điểm gửi xe',
                },
                { key: 'branchId', type: this.dataType.string, name: 'Cơ sở' },
                {
                  key: 'description',
                  type: this.dataType.string,
                  name: 'Mô tả',
                  min: 5,
                },
                {
                  key: 'price',
                  type: this.dataType.number,
                  name: 'Giá',
                  min: 5,
                },
                {
                  key: 'typeService',
                  type: this.dataType.number,
                  name: 'Tình trạng',
                },
                {
                  key: 'shortDes',
                  type: this.dataType.string,
                  name: 'Mô tả ngắn',
                },
              ],
            },
          },
        },
        methods: {
          get: [
            async function (req, res) {
              let brands = await ServicesModel.MODEL.getServicesByCondition({
                userId: req.user._id,
                status: 1,
              });
              return ChildRouter.renderOrResponse(req, res, { brands });
            },
          ],

          post: [
            async function (req, res) {
              let {
                name,
                storeId,
                branchId,
                description,
                price,
                typeService,
                classify,
                shortDes,
                pictureOlds
              } = req.body;
              classify = JSON.parse(classify);
              classify.forEach((item) => {
                item.data = item?.data.filter(
                  (item) =>
                    !validate.isEmpty(item.name) &&
                    !validate.isEmpty(item.price)
                );
              });
              let obj = {
                userId: req.user._id,
                name,
                nameUTF: StringUtils.removeUtf8(name),
                descriptionUTF: StringUtils.removeUtf8(description),
                storeId,
                branchId,
                description,
                price,
                typeService,
                classify,
                shortDes,
                status: 3
              };

              if (!storeId || storeId == 'null' || storeId == '') {
                return ChildRouter.responseError(
                  'Vui lòng chọn thương hiệu',
                  res
                );
              }

              if (req.upload &&
                  req.upload.pictures &&
                  req.upload.pictures.length > 0
              ) {
                obj.pictures = [];
                for (let j = 0; j < req.upload.pictures.length; j++) {
                  obj.pictures.push(req.upload.pictures[j].path);
                }
              } else {
                return ChildRouter.responseError('Cần tải lên đủ ảnh', res);
              }

              if (req.upload && req.upload.thumbail) {
                obj.thumbail = req.upload.thumbail[0].path;
              } else {
                return ChildRouter.responseError('Cần tải lên ảnh phòng', res);
              }
              // Ảnh thêm
              // if (req.upload && req.upload.pictures && req.upload.pictures.length === 3) {
              //     obj.pictures = [req.upload.pictures[0].path,req.upload.pictures[1].path,req.upload.pictures[2].path]
              // } else {
              //     return ChildRouter.responseError('Cần tải lên đủ ảnh.',res);
              // }
              // End ảnh

              await RoomOfHotelModel.MODEL.addRoom(obj);
              // Update Tags for Brand
              await ServiceUpdateData.updateTags(storeId, obj.name);
              // End Update Tags for Brand
              return ChildRouter.responseSuccess(
                'Nội dung sẽ được hiển thị sau khi hệ thống kiểm duyệt nội dung',
                res
              );
            },
          ],
        },
      },
      '/edit-iparking/:productId.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/room/room-edit.ejs',
          get: 'view',
          title: 'Chỉnh sửa Iparking',
          upload: [
            { name: 'thumbail', maxCount: 1 },
            { name: 'pictures', maxCount: 15 },
          ],
          post: 'json',
          validate: {
            body: {
              method: ['post'],
              validate: [
                {
                  key: 'name',
                  type: this.dataType.string,
                  name: 'Tên dịch vụ',
                  min: 5,
                  max: 200,
                },
                {
                  key: 'storeId',
                  type: this.dataType.string,
                  name: 'Điểm gửi xe',
                },
                { key: 'branchId', type: this.dataType.string, name: 'Cơ sở' },
                {
                  key: 'description',
                  type: this.dataType.string,
                  name: 'Mô tả',
                  min: 5,
                },
                {
                  key: 'price',
                  type: this.dataType.number,
                  name: 'Giá',
                  min: 5,
                },
                {
                  key: 'typeService',
                  type: this.dataType.number,
                  name: 'Tình trạng',
                },
                {
                  key: 'shortDes',
                  type: this.dataType.string,
                  name: 'Mô tả ngắn',
                },
              ],
            },
          },
        },

        methods: {
          get: [
            async function (req, res) {
              let brands = await ServicesModel.MODEL.getServicesByCondition({
                userId: req.user._id,
                status: 1,
              });
              let room = await RoomOfHotelModel.MODEL.getRoomById(
                req.params.productId
              );
              let danhsachCoSo = await BranchModel.MODEL.getBranchByCondition({
                status: { $ne: 2 },
                storeId: room.storeId,
              });

              if (room && !room.classify) room.classify = [];
              return ChildRouter.renderOrResponse(req, res, {
                room,
                brands,
                danhsachCoSo,
                // branchIds
              });
            },
          ],

          post: [
            async function (req, res) {
              let {
                name,
                storeId,
                branchId,
                description,
                classify,
                price,
                typeService,
                shortDes,
                pictureOlds
              } = req.body;
              classify = JSON.parse(classify);
              classify.forEach((item) => {
                item.data = item?.data.filter(
                  (item) =>
                    !validate.isEmpty(item.name) &&
                    !validate.isEmpty(item.price)
                );
              });
              let obj = {
                userId: req.user._id,
                name,
                nameUTF: StringUtils.removeUtf8(name),
                descriptionUTF: StringUtils.removeUtf8(description),
                storeId,
                branchId,
                description,
                price,
                typeService,
                classify,
                shortDes,
              };

              pictureOlds = JSON.parse(pictureOlds);

              if (
                  req.upload &&
                  req.upload.pictures &&
                  req.upload.pictures.length > 0
              ) {
                let pictures = req.upload.pictures;
                pictures.forEach((img, index) => {
                  if (!pictureOlds.includes(img))
                  {
                    pictureOlds.push(img.path);
                  }
                });
              }
              obj.pictures = pictureOlds;

              let room = await RoomOfHotelModel.MODEL.getRoomById(
                req.params.productId
              );
              if (req.upload && req.upload.thumbail) {
                FileUtils.deleteFile(APP.BASE_DIR + room.thumbail);
                obj.thumbail = req.upload.thumbail[0].path;
              }
              //Update DB
              await RoomOfHotelModel.MODEL.updateRoom(
                req.params.productId,
                obj
              );
              // Update Tags for Brand
              await ServiceUpdateData.updateTags(storeId, obj.name);
              // End Update Tags for Brand
              return ChildRouter.responseSuccess('Thành công', res);
            },
          ],
        },
      },
      // End Room
      // Clinic
      '/mng-garage.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/clinic/clinic-list.ejs',
          get: 'view',
          title: 'Quản lý dịch vụ Gara',
        },
        methods: {
          get: [
            async function (req, res) {
              let categories = await ServiceOfClinicModel.MODEL.getAllClinics();
              let objCategories = {};
              categories.forEach((item) => {
                objCategories[item._id] = item.name;
              });
              return ChildRouter.renderOrResponse(req, res, { categories });
            },
          ],
        },
      },

      '/classify/:classifyServiceType': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/classification-of-brand/list.ejs',
          get: 'view',
          title: 'Quản lý dịch vụ mở rộng của thương hiệu',
        },
        methods: {
          get: [
            async function (req, res) {
              // let categories = await ServiceOfClinicModel.MODEL.getAllClinics();
              // let objCategories = {};
              // categories.forEach((item) => {
              //   objCategories[item._id] = item.name;
              // });
              // let classifyServiceType = '';
              // if(req.params.classifyServiceType === 'table'){
              //   classifyServiceType = 'đặt bàn';
              // }
              return ChildRouter.renderOrResponse(req, res, { classifyServiceType: req.params.classifyServiceType });
            },
          ],
        },
      },
      '/add-classify/:classifyServiceType': {
        config: {
          auth: [this.roles.all],
          view: 'public/index.ejs',
          inc: 'inc/store/classification-of-brand/add.ejs',
          get: 'view',
          title: 'Thêm mới',
          upload: [
            { name: 'thumbail', maxCount: 1 },
            { name: 'pictures', maxCount: 15 },
          ],
          post: 'json',
          validate: {
            body: {
              method: ['post'],
              validate: [
                {
                  key: 'name',
                  type: this.dataType.string,
                  name: 'Tên dịch vụ',
                  min: 5,
                  max: 200,
                },
                {
                  key: 'storeId',
                  type: this.dataType.string,
                  name: 'Thương hiệu',
                },
                { key: 'branchId', type: this.dataType.string, name: 'Cơ sở' },
                {
                  key: 'categoryId',
                  type: this.dataType.string,
                  name: 'Danh mục',
                },
                {
                  key: 'description',
                  type: this.dataType.string,
                  name: 'Mô tả',
                  min: 5,
                },
                // {key: 'price', type: this.dataType.number, name: 'Giá', min: 5},
                {
                  key: 'typeService',
                  type: this.dataType.number,
                  name: 'Tình trạng',
                },
                {
                  key: 'shortDes',
                  type: this.dataType.string,
                  name: 'Mô tả ngắn',
                },
              ],
            },
          },
        },
        methods: {
          get: [
            async function (req, res) {
              let categories = await CategoryModel.MODEL.getCategoryByCondition({type: { $in: [ 3 ] }});
              let brands = await ServicesModel.MODEL.getServicesByCondition({
                userId: req.user._id,
                status: 1,
              });
              return ChildRouter.renderOrResponse(req, res, { categories, brands, classifyServiceType: req.params.classifyServiceType });
            },
          ],

          post: [
            async function (req, res) {
              let {
                name,
                storeId,
                branchId,
                categoryId,
                description,
                price,
                typeService,
                classify,
                shortDes,
              } = req.body;
              classify = JSON.parse(classify);
              classify.forEach((item) => {
                item.data = item?.data.filter(
                  (item) =>
                    !validate.isEmpty(item.name) &&
                    !validate.isEmpty(item.price)
                );
              });
              let obj = {
                userId: req.user._id,
                name,
                nameUTF: StringUtils.removeUtf8(name),
                descriptionUTF: StringUtils.removeUtf8(description),
                storeId,
                branchId,
                categoryId,
                description,
                price,
                typeService,
                classify,
                shortDes,
                classifyServiceType: req.params.classifyServiceType,
                status: 1 // mặc định đc duyệt
              };

              if (categoryId == 'null') {
                return ChildRouter.responseError(
                  'Vui lòng chọn danh mục',
                  res
                );
              }

              if (!storeId || storeId == 'null' || storeId == '') {
                return ChildRouter.responseError(
                  'Vui lòng chọn thương hiệu',
                  res
                );
              }

              if (req.upload &&
                  req.upload.pictures &&
                  req.upload.pictures.length > 0
              ) {
                obj.pictures = [];
                for (let j = 0; j < req.upload.pictures.length; j++) {
                  obj.pictures.push(req.upload.pictures[j].path);
                }
              } else {
                return ChildRouter.responseError('Cần tải lên đủ ảnh', res);
              }

              // ảnh đại diện
              if (req.upload && req.upload.thumbail) {
                obj.thumbail = req.upload.thumbail[0].path;
              } else {
                return ChildRouter.responseError(
                  'Cần tải lên ảnh dịch vụ',
                  res
                );
              }

              await ClassificationOfBrandModel.MODEL.add(obj);
              // Update Tags for Brand
              await ServiceUpdateData.updateTags(storeId, obj.name);
              // End Update Tags for Brand
              return ChildRouter.responseSuccess(
                'Nội dung sẽ được hiển thị sau khi hệ thống kiểm duyệt nội dung',
                res
              );
            },
          ],
        },
      },
      '/get-classify/:classifyServiceType/:status': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },
        methods: {
          get: [
            async function (req, res) {
              let { status, classifyServiceType } = req.params;
              let products =
                await ClassificationOfBrandModel.MODEL.getServiceOfBrand(
                  {
                    userId: req.user._id,
                    status: Number(status),
                    classifyServiceType: classifyServiceType
                  },
                  { createAt: -1 }
                );
              return ChildRouter.responseSuccess('Thành công', res, {
                products,
              });
            },
          ],
        },
      },

      '/get-clinic.html/:status': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },
        methods: {
          get: [
            async function (req, res) {
              let { status } = req.params;
              let products =
                await ServiceOfClinicModel.MODEL.getClinicByConditionForStore(
                  {
                    userId: req.user._id,
                    status: Number(status),
                  },
                  { createAt: -1 }
                );
              return ChildRouter.responseSuccess('Thành công', res, {
                products,
              });
            },
          ],
        },
      },
      '/update-status-clinic/:id.html': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              let product = await ServiceOfClinicModel.MODEL.getDataById(
                  req.params.id.trim()
              );
              if (
                  !product ||
                  product.userId != req.user._id
              ) {
                return ChildRouter.responseError('Yêu cầu không hợp lệ', res);
              }

              await ServiceOfClinicModel.MODEL.updateById(
                  req.params.id.trim(),
                  { status: Number(req.query.status) },
              );
              return ChildRouter.responseSuccess('Thành công', res);
            },
          ],
        },
      },

      '/create-garage.html': {
        config: {
          auth: [this.roles.all],
          view: 'public/index.ejs',
          inc: 'inc/store/clinic/clinic-add.ejs',
          get: 'view',
          title: 'Thêm mới dịch vụ gara',
          upload: [
            { name: 'thumbail', maxCount: 1 },
            { name: 'pictures', maxCount: 15 },
          ],
          post: 'json',
          validate: {
            body: {
              method: ['post'],
              validate: [
                {
                  key: 'name',
                  type: this.dataType.string,
                  name: 'Tên dịch vụ',
                  min: 5,
                  max: 200,
                },
                {
                  key: 'storeId',
                  type: this.dataType.string,
                  name: 'Thương hiệu',
                },
                { key: 'branchId', type: this.dataType.string, name: 'Cơ sở' },
                {
                  key: 'description',
                  type: this.dataType.string,
                  name: 'Mô tả',
                  min: 5,
                },
                // {key: 'price', type: this.dataType.number, name: 'Giá', min: 5},
                {
                  key: 'typeService',
                  type: this.dataType.number,
                  name: 'Tình trạng',
                },
                {
                  key: 'shortDes',
                  type: this.dataType.string,
                  name: 'Mô tả ngắn',
                },
              ],
            },
          },
        },
        methods: {
          get: [
            async function (req, res) {
              let categories = await CategoryModel.MODEL.getCategoryByCondition({type: { $in: [ 3 ] }});
              let brands = await ServicesModel.MODEL.getServicesByCondition({
                userId: req.user._id,
                status: 1,
              });
              return ChildRouter.renderOrResponse(req, res, { categories, brands });
            },
          ],

          post: [
            async function (req, res) {
              let {
                name,
                storeId,
                branchId,
                description,
                price,
                typeService,
                classify,
                shortDes,
              } = req.body;
              classify = JSON.parse(classify);
              classify.forEach((item) => {
                item.data = item?.data.filter(
                  (item) =>
                    !validate.isEmpty(item.name) &&
                    !validate.isEmpty(item.price)
                );
              });
              let obj = {
                userId: req.user._id,
                name,
                nameUTF: StringUtils.removeUtf8(name),
                descriptionUTF: StringUtils.removeUtf8(description),
                storeId,
                branchId,
                description,
                price,
                typeService,
                classify,
                shortDes,
                status: 3
              };

              if (!storeId || storeId == 'null' || storeId == '') {
                return ChildRouter.responseError(
                  'Vui lòng chọn thương hiệu',
                  res
                );
              }

              if (req.upload &&
                  req.upload.pictures &&
                  req.upload.pictures.length > 0
              ) {
                obj.pictures = [];
                for (let j = 0; j < req.upload.pictures.length; j++) {
                  obj.pictures.push(req.upload.pictures[j].path);
                }
              } else {
                return ChildRouter.responseError('Cần tải lên đủ ảnh', res);
              }

              if (req.upload && req.upload.thumbail) {
                obj.thumbail = req.upload.thumbail[0].path;
              } else {
                return ChildRouter.responseError(
                  'Cần tải lên ảnh dịch vụ',
                  res
                );
              }

              await ServiceOfClinicModel.MODEL.addClinic(obj);
              // Update Tags for Brand
              await ServiceUpdateData.updateTags(storeId, obj.name);
              // End Update Tags for Brand
              return ChildRouter.responseSuccess(
                'Dịch vụ gara sẽ được hiển thị sau khi hệ thống kiểm duyệt nội dung',
                res
              );
            },
          ],
        },
      },

      '/edit-garage/:productId.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/clinic/clinic-edit.ejs',
          get: 'view',
          title: 'Chỉnh sửa dịch vụ gara',
          upload: [
            { name: 'thumbail', maxCount: 1 },
            { name: 'pictures', maxCount: 15 },
          ],
          post: 'json',
          validate: {
            body: {
              method: ['post'],
              validate: [
                {
                  key: 'name',
                  type: this.dataType.string,
                  name: 'Tên dịch vụ',
                  min: 5,
                  max: 200,
                },
                {
                  key: 'storeId',
                  type: this.dataType.string,
                  name: 'Thương hiệu',
                },
                { key: 'branchId', type: this.dataType.string, name: 'Cơ sở' },
                { key: 'categoryId', type: this.dataType.string, name: 'Danh mục' },
                {
                  key: 'description',
                  type: this.dataType.string,
                  name: 'Mô tả',
                  min: 5,
                },
                // {key: 'price',type: this.dataType.number,name: 'Giá',min: 1},
                {
                  key: 'typeService',
                  type: this.dataType.number,
                  name: 'Tình trạng',
                },
                {
                  key: 'shortDes',
                  type: this.dataType.string,
                  name: 'Mô tả ngắn',
                },
              ],
            },
          },
        },

        methods: {
          get: [
            async function (req, res) {
              let categories = await CategoryModel.MODEL.getCategoryByCondition({type: { $in: [ 3 ] }});

              let brands = await ServicesModel.MODEL.getServicesByCondition({
                userId: req.user._id,
                status: 1,
              });
              let clinic = await ServiceOfClinicModel.MODEL.getClinicById(
                req.params.productId
              );
              let danhsachCoSo = await BranchModel.MODEL.getBranchByCondition({
                status: { $ne: 2 },
                storeId: clinic.storeId,
              });

              if (clinic && !clinic.classify) clinic.classify = [];
              return ChildRouter.renderOrResponse(req, res, {
                categories,
                clinic,
                brands,
                danhsachCoSo,
                // branchIds
              });
            },
          ],

          post: [
            async function (req, res) {
              let {
                name,
                storeId,
                branchId,
                categoryId,
                description,
                classify,
                price,
                typeService,
                shortDes,
                pictureOlds
              } = req.body;
              classify = JSON.parse(classify);
              classify.forEach((item) => {
                item.data = item?.data.filter(
                  (item) =>
                    !validate.isEmpty(item.name) &&
                    !validate.isEmpty(item.price)
                );
              });
              let obj = {
                userId: req.user._id,
                name,
                nameUTF: StringUtils.removeUtf8(name),
                descriptionUTF: StringUtils.removeUtf8(description),
                storeId,
                branchId,
                categoryId,
                description,
                price,
                typeService,
                classify,
                shortDes,
              };

              pictureOlds = JSON.parse(pictureOlds);

              if (
                  req.upload &&
                  req.upload.pictures &&
                  req.upload.pictures.length > 0
              ) {
                let pictures = req.upload.pictures;
                pictures.forEach((img, index) => {
                  if (!pictureOlds.includes(img))
                  {
                    pictureOlds.push(img.path);
                  }
                });
              }
              obj.pictures = pictureOlds;

              let room = await ServiceOfClinicModel.MODEL.getClinicById(
                req.params.productId
              );
              if (req.upload && req.upload.thumbail) {
                FileUtils.deleteFile(APP.BASE_DIR + room.thumbail);
                obj.thumbail = req.upload.thumbail[0].path;
              }
              //Update DB
              await ServiceOfClinicModel.MODEL.updateClinic(
                req.params.productId,
                obj
              );
              // Update Tags for Brand
              await ServiceUpdateData.updateTags(storeId, obj.name);
              // End Update Tags for Brand
              return ChildRouter.responseSuccess('Thành công', res);
            },
          ],
        },
      },

      '/edit-classify/:classifyServiceType/:id': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/classification-of-brand/edit.ejs',
          get: 'view',
          title: 'Chỉnh sửa',
          upload: [
            { name: 'thumbail', maxCount: 1 },
            { name: 'pictures', maxCount: 15 },
          ],
          post: 'json',
          validate: {
            body: {
              method: ['post'],
              validate: [
                {
                  key: 'name',
                  type: this.dataType.string,
                  name: 'Tên dịch vụ',
                  min: 5,
                  max: 200,
                },
                {
                  key: 'storeId',
                  type: this.dataType.string,
                  name: 'Thương hiệu',
                },
                { key: 'branchId', type: this.dataType.string, name: 'Cơ sở' },
                {
                  key: 'categoryId',
                  type: this.dataType.string,
                  name: 'Danh mục',
                },
                {
                  key: 'description',
                  type: this.dataType.string,
                  name: 'Mô tả',
                  min: 5,
                },
                // {key: 'price',type: this.dataType.number,name: 'Giá',min: 1},
                {
                  key: 'typeService',
                  type: this.dataType.number,
                  name: 'Tình trạng',
                },
                {
                  key: 'shortDes',
                  type: this.dataType.string,
                  name: 'Mô tả ngắn',
                },
              ],
            },
          },
        },

        methods: {
          get: [
            async function (req, res) {
              let categories = await CategoryModel.MODEL.getCategoryByCondition({type: { $in: [ 3 ] }});
              let brands = await ServicesModel.MODEL.getServicesByCondition({
                userId: req.user._id,
                status: 1,
              });
              let service =
                await ClassificationOfBrandModel.MODEL.getServiceById(
                  req.params.id
                );
              let danhsachCoSo = await BranchModel.MODEL.getBranchByCondition({
                status: { $ne: 2 },
                storeId: service.storeId,
              });

              if (service && !service.classify) service.classify = [];
              return ChildRouter.renderOrResponse(req, res, {
                categories,
                service,
                brands,
                danhsachCoSo,
                classifyServiceType: req.params.classifyServiceType
                // branchIds
              });
            },
          ],

          post: [
            async function (req, res) {
              let {
                name,
                storeId,
                branchId,
                categoryId,
                description,
                classify,
                price,
                typeService,
                shortDes,
                pictureOlds
              } = req.body;

              classify = JSON.parse(classify);
              classify.forEach((item) => {
                item.data = item?.data.filter(
                  (item) =>
                    !validate.isEmpty(item.name) &&
                    !validate.isEmpty(item.price)
                );
              });

              // fix bug nếu là 1 cơ sở thì client post string cần convert to array
              if(!Array.isArray(branchId)){
                branchId = [branchId]
              }

              let obj = {
                userId: req.user._id,
                name,
                nameUTF: StringUtils.removeUtf8(name),
                descriptionUTF: StringUtils.removeUtf8(description),
                storeId,
                branchId,
                categoryId,
                description,
                price,
                typeService,
                classify,
                shortDes,
              };

              if (categoryId == 'null') {
                return ChildRouter.responseError('Danh mục không phù hợp', res);
              }

              let classification = await ClassificationOfBrandModel.MODEL.getDataById(
                req.params.id
              );

              pictureOlds = JSON.parse(pictureOlds);

              if (
                  req.upload &&
                  req.upload.pictures &&
                  req.upload.pictures.length > 0
              ) {
                let pictures = req.upload.pictures;
                pictures.forEach((img, index) => {
                  if (!pictureOlds.includes(img))
                  {
                    pictureOlds.push(img.path);
                  }
                });
              }
              obj.pictures = pictureOlds;

              classification.pictures.forEach((item) => {
                if (obj.pictures && !obj.pictures.includes(item)) {
                  FileUtils.deleteFile(APP.BASE_DIR + item);
                }
              });

              if (req.upload && req.upload.thumbail) {
                FileUtils.deleteFile(APP.BASE_DIR + classification.thumbail);
                obj.thumbail = req.upload.thumbail[0].path;
              }
              //Update DB
              await ClassificationOfBrandModel.MODEL.updateServiceOfBrand(
                req.params.id,
                obj
              );
              // Update Tags for Brand
              await ServiceUpdateData.updateTags(storeId, obj.name);
              // End Update Tags for Brand
              return ChildRouter.responseSuccess('Thành công', res);
            },
          ],
        },
      },
      // End Clinic
      '/tai-san-pham.html/:status': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              let { status } = req.params;

              let categories = await CategoryModel.MODEL.getAllCategory();
              let objCategories = {};
              categories.forEach((item) => {
                objCategories[item._id] = item.name;
              });
              let products =
                await ProducModel.MODEL.getProductByConditionForStore(
                  {
                    userId: req.user._id,
                    status: Number(status),
                  },
                  { createAt: -1 }
                );

              products.forEach((item) => {
                item.categoryName = objCategories[item.categoryId];
              });

              return ChildRouter.responseSuccess('Thành công', res, {
                products,
              });
            },
          ],
        },
      },

      '/get-service.html/:status': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },
        methods: {
          get: [
            async function (req, res) {
              let { status } = req.params;

              let categories = await CategoryModel.MODEL.getAllCategory();
              let objCategories = {};
              categories.forEach((item) => {
                objCategories[item._id] = item.name;
              });

              let products = await ServiceOfSpaModel.MODEL.getServiceByConditionForStore(
                  {
                    userId: req.user._id,
                    status: Number(status),
                  },
                  { createAt: -1 }
                );

              products.forEach((item) => {
                item.categoryName = objCategories[item.categoryId];
              });

              return ChildRouter.responseSuccess('Thành công', res, {
                products,
              });
            },
          ],
        },
      },
      '/hide-product/:productId.html': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              let product = await ProducModel.MODEL.getDataById(
                req.params.productId.trim()
              );
              if (
                !product ||
                product.userId != req.user._id ||
                product.status == 0
              ) {
                return ChildRouter.responseError('Yêu cầu không hợp lệ', res);
              }

              await ProducModel.MODEL.updateProducts(
                req.params.productId.trim(),
                { status: 2 }
              );
              return ChildRouter.responseSuccess('Thành công', res);
            },
          ],
        },
      },

      '/show-product/:productId.html': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              let product = await ProducModel.MODEL.getDataById(
                req.params.productId.trim()
              );
              if (
                !product ||
                product.userId != req.user._id ||
                product.status == 0
              ) {
                return ChildRouter.responseError('Yêu cầu không hợp lệ', res);
              }

              await ProducModel.MODEL.updateProducts(
                req.params.productId.trim(),
                { status: 1 }
              );
              return ChildRouter.responseSuccess('Thành công', res);
            },
          ],
        },
      },

      '/delete-product/:productId.html': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              await ProducModel.MODEL.updateProducts(req.params.productId, {
                status: 2,
              });
              return ChildRouter.responseSuccess('Thành công', res);
            },
          ],
        },
      },

      '/create-product': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/them-moi-san-pham.ejs',
          get: 'view',
          title: 'Thêm mới sản phẩm',
          upload: [
            { name: 'thumbail', maxCount: 1 },
            { name: 'pictures', maxCount: 15 },
          ],
          post: 'json',
          validate: {
            body: {
              method: ['post'],
              validate: [
                {
                  key: 'name',
                  type: this.dataType.string,
                  name: 'Tên sản phẩm',
                  min: 5,
                  max: 200,
                },
                {
                  key: 'storeId',
                  type: this.dataType.string,
                  name: 'Cửa hàng',
                },
                { key: 'branchId', type: this.dataType.string, name: 'Cơ sở' },
                {
                  key: 'categoryId',
                  type: this.dataType.string,
                  name: 'Danh mục',
                },
                {
                  key: 'description',
                  type: this.dataType.string,
                  name: 'Mô tả',
                  min: 5,
                },
                {
                  key: 'trademark',
                  type: this.dataType.string,
                  name: 'Thương hiệu',
                  min: 2,
                  max: 200,
                },
                {
                  key: 'price',
                  type: this.dataType.number,
                  name: 'Giá',
                  min: 5,
                },
                {
                  key: 'typeProduct',
                  type: this.dataType.number,
                  name: 'Tình trạng',
                },
                {
                  key: 'transport',
                  type: this.dataType.string,
                  name: 'Vận chuyển',
                  min: 2,
                },
                {
                  key: 'weight',
                  type: this.dataType.number,
                  name: 'Khối lượng vận chuyển',
                },
              ],
            },
          },
        },

        methods: {
          get: [
            async function (req, res) {
              let categories = await CategoryModel.MODEL.getAllCategory();
              let stores = await ServicesModel.MODEL.getServicesByCondition({
                userId: req.user._id,
                // type: 0,
                status: 1,
              });
              return ChildRouter.renderOrResponse(req, res, {
                categories,
                stores,
              });
            },
          ],

          post: [
            async function (req, res) {
              let {
                name,
                storeId,
                categoryId,
                branchId,
                description,
                trademark,
                price,
                priceOld,
                typeProduct,
                typeShip,
                classify,
                transport,
                weight,
              } = req.body;
              classify = JSON.parse(classify);
              let obj = {
                userId: req.user._id,
                name,
                nameUTF: StringUtils.removeUtf8(name),
                descriptionUTF: StringUtils.removeUtf8(description),
                storeId,
                branchId,
                categoryId,
                description,
                trademark,
                price,
                priceOld,
                typeProduct,
                typeShip,
                classify,
                transport,
                weight: Number(weight),
                status: 3 // chờ duyệt
              };
              if (categoryId == 'null') {
                return ChildRouter.responseError(
                  'Vui lòng chọn danh mục sản phẩm',
                  res
                );
              }

              if (!storeId || storeId == 'null' || storeId == '') {
                return ChildRouter.responseError(
                  'Vui lòng chọn cửa hàng hoặc tạo cửa hàng mới',
                  res
                );
              }

              if (
                req.upload &&
                req.upload.pictures &&
                req.upload.pictures.length > 0
              ) {
                obj.pictures = [];
                for (let j = 0; j < req.upload.pictures.length; j++) {
                  obj.pictures.push(req.upload.pictures[j].path);
                }
              } else {
                return ChildRouter.responseError('Cần tải lên đủ ảnh', res);
              }

              // ảnh đại diện
              if (req.upload && req.upload.thumbail) {
                obj.thumbail = req.upload.thumbail[0].path;
              }

              const result = await ProducModel.MODEL.addProducts(obj);
              if (result != null) {
                return ChildRouter.responseSuccess(
                    'Sản phẩm sẽ được hiển thị sau khi hệ thống kiểm duyệt nội dung',
                    res,
                    { result }
                );
              } else {
                return  ChildRouter.responseError('Xảy ra lỗi thêm sản phẩm, cần kiểm tra lại data', res, {})
              }
            },
          ],
        },
      },

      '/mng-spa-wash.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/spa/spa-list.ejs',
          get: 'view',
          title: 'Quản lý dịch vụ',
        },
        methods: {
          get: [
            async function (req, res) {
              let categories = await ServiceOfSpaModel.MODEL.getAllService();
              let objCategories = {};
              categories.forEach((item) => {
                objCategories[item._id] = item.name;
              });
              return ChildRouter.renderOrResponse(req, res, { categories });
            },
          ],
        },
      },

      '/update-status-spa/:id.html': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              let product = await ServiceOfSpaModel.MODEL.getDataById(
                  req.params.id.trim()
              );
              if (
                  !product ||
                  product.userId != req.user._id
              ) {
                return ChildRouter.responseError('Yêu cầu không hợp lệ', res);
              }

              await ServiceOfSpaModel.MODEL.updateById(
                  req.params.id.trim(),
                  { status: Number(req.query.status) },
              );
              return ChildRouter.responseSuccess('Thành công', res);
            },
          ],
        },
      },

      '/create-spa-wash.html': {
        config: {
          auth: [this.roles.all],
          view: 'public/index.ejs',
          inc: 'inc/store/spa/spa-add.ejs',
          get: 'view',
          title: 'Thêm mới dịch vụ',
          upload: [
            { name: 'thumbail', maxCount: 1 },
            { name: 'pictures', maxCount: 15 },
          ],
          post: 'json',
          validate: {
            body: {
              method: ['post'],
              validate: [
                {
                  key: 'name',
                  type: this.dataType.string,
                  name: 'Tên dịch vụ',
                  min: 5,
                  max: 200,
                },
                { key: 'storeId', type: this.dataType.string, name: 'Spa' },
                { key: 'branchId', type: this.dataType.string, name: 'Cơ sở' },
                { key: 'categoryId', type: this.dataType.string, name: 'Danh mục'},
                {
                  key: 'description',
                  type: this.dataType.string,
                  name: 'Mô tả',
                  min: 5,
                },
                {
                  key: 'price',
                  type: this.dataType.number,
                  name: 'Giá',
                  min: 5,
                },
                {
                  key: 'typeService',
                  type: this.dataType.number,
                  name: 'Tình trạng',
                },
                {
                  key: 'shortDes',
                  type: this.dataType.string,
                  name: 'Mô tả ngắn',
                },
              ],
            },
          },
        },
        methods: {
          get: [
            async function (req, res) {
              let categories = await CategoryModel.MODEL.getCategoryByCondition({type: { $in: [ 3 ] }});
              let stores = await ServicesModel.MODEL.getServicesByCondition({
                userId: req.user._id,
                status: 1,
              });
              return ChildRouter.renderOrResponse(req, res, {
                categories,
                stores,
              });
            },
          ],

          post: [
            async function (req, res) {
              let {
                name,
                storeId,
                categoryId,
                branchId,
                description,
                price,
                typeService,
                classify,
                shortDes,
              } = req.body;
              classify = JSON.parse(classify);
              classify.forEach((item) => {
                item.data = item?.data.filter(
                  (item) =>
                    !validate.isEmpty(item.name) &&
                    !validate.isEmpty(item.price)
                );
              });
              let obj = {
                userId: req.user._id,
                name,
                nameUTF: StringUtils.removeUtf8(name),
                descriptionUTF: StringUtils.removeUtf8(description),
                storeId,
                branchId,
                categoryId,
                description,
                price,
                typeService,
                classify,
                shortDes,
                status: 3
              };
              if (categoryId == 'null') {
                return ChildRouter.responseError(
                  'Vui lòng chọn danh mục',
                  res
                );
              }

              if (!storeId || storeId == 'null' || storeId == '') {
                return ChildRouter.responseError(
                  'Vui lòng chọn cửa hàng hoặc tạo cửa hàng mới',
                  res
                );
              }

              if (req.upload &&
                  req.upload.pictures &&
                  req.upload.pictures.length > 0
              ) {
                obj.pictures = [];
                for (let j = 0; j < req.upload.pictures.length; j++) {
                  obj.pictures.push(req.upload.pictures[j].path);
                }
              } else {
                return ChildRouter.responseError('Cần tải lên đủ ảnh', res);
              }

              if (req.upload && req.upload.thumbail) {
                obj.thumbail = req.upload.thumbail[0].path;
              } else {
                return ChildRouter.responseError(
                  'Cần tải lên ảnh dịch vụ',
                  res
                );
              }
              // Ảnh thêm
              // if (req.upload && req.upload.pictures && req.upload.pictures.length === 3) {
              //     obj.pictures = [req.upload.pictures[0].path,req.upload.pictures[1].path,req.upload.pictures[2].path]
              // } else {
              //     return ChildRouter.responseError('Cần tải lên đủ ảnh.',res);
              // }
              // End ảnh

              await ServiceOfSpaModel.MODEL.addService(obj);
              // Update Tags for Brand
              await ServiceUpdateData.updateTags(storeId, obj.name);
              // End Update Tags for Brand
              return ChildRouter.responseSuccess(
                'Dịch vụ sẽ được hiển thị sau khi hệ thống kiểm duyệt nội dung',
                res
              );
            },
          ],
        },
      },

      '/edit-spa-wash/:productId.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/spa/spa-edit.ejs',
          get: 'view',
          title: 'Chỉnh sửa dịch vụ',
          upload: [
            { name: 'thumbail', maxCount: 1 },
            { name: 'pictures', maxCount: 15 },
          ],
          post: 'json',
          validate: {
            body: {
              method: ['post'],
              validate: [
                {
                  key: 'name',
                  type: this.dataType.string,
                  name: 'Tên dịch vụ',
                  min: 5,
                  max: 200,
                },
                { key: 'storeId', type: this.dataType.string, name: 'Spa' },
                { key: 'branchId', type: this.dataType.string, name: 'Cơ sở' },
                { key: 'categoryId', type: this.dataType.string, name: 'Danh mục'},
                {
                  key: 'description',
                  type: this.dataType.string,
                  name: 'Mô tả',
                  min: 5,
                },
                {
                  key: 'price',
                  type: this.dataType.number,
                  name: 'Giá',
                  min: 5,
                },
                {
                  key: 'typeService',
                  type: this.dataType.number,
                  name: 'Tình trạng',
                },
                {
                  key: 'shortDes',
                  type: this.dataType.string,
                  name: 'Mô tả ngắn',
                },
              ],
            },
          },
        },

        methods: {
          get: [
            async function (req, res) {
              let categories = await CategoryModel.MODEL.getCategoryByCondition({type: { $in: [ 3 ] }});

              let brands = await ServicesModel.MODEL.getServicesByCondition({
                userId: req.user._id,
                status: 1,
              });
              let branch = await BranchModel.MODEL.getBranchById(
                req.params.branchId
              );
              let product = await ServiceOfSpaModel.MODEL.getServiceById(
                req.params.productId
              );
              let danhsachCoSo = await BranchModel.MODEL.getBranchByCondition({
                status: { $ne: 2 },
                storeId: product.storeId,
              });
              // console.log("product", product)
              if (product && !product.classify) product.classify = [];
              return ChildRouter.renderOrResponse(req, res, {
                categories,
                product,
                brands,
                danhsachCoSo,
                branch,
              });
            },
          ],

          post: [
            async function (req, res) {
              let {
                name,
                storeId,
                branchId,
                categoryId,
                description,
                classify,
                price,
                typeService,
                shortDes,
                pictureOlds
              } = req.body;
              classify = JSON.parse(classify);
              classify.forEach((item) => {
                // item.data = item?.data.filter(item => item.name !== "" && item.price !== "")
                item.data = item?.data.filter(
                  (item) =>
                    !validate.isEmpty(item.name) &&
                    !validate.isEmpty(item.price)
                );
              });

              // fix bug nếu là 1 cơ sở thì client post string cần convert to array
              if(!Array.isArray(branchId)){
                branchId = [branchId]
              }

              let obj = {
                userId: req.user._id,
                name,
                nameUTF: StringUtils.removeUtf8(name),
                descriptionUTF: StringUtils.removeUtf8(description),
                storeId,
                branchId,
                categoryId,
                description,
                price,
                typeService,
                classify,
                shortDes,
              };

              pictureOlds = JSON.parse(pictureOlds);

              if (
                  req.upload &&
                  req.upload.pictures &&
                  req.upload.pictures.length > 0
              ) {
                let pictures = req.upload.pictures;
                pictures.forEach((img, index) => {
                  if (!pictureOlds.includes(img))
                  {
                    pictureOlds.push(img.path);
                  }
                });
              }
              obj.pictures = pictureOlds;

              let product = await ServiceOfSpaModel.MODEL.getServiceById(
                req.params.productId
              );
              if (req.upload && req.upload.thumbail) {
                FileUtils.deleteFile(APP.BASE_DIR + product.thumbail);
                obj.thumbail = req.upload.thumbail[0].path;
              }
              await ServiceOfSpaModel.MODEL.updateService(
                req.params.productId,
                obj
              );
              // Update Tags for Brand
              await ServiceUpdateData.updateTags(storeId, obj.name);
              // End Update Tags for Brand
              return ChildRouter.responseSuccess('Thành công', res);
            },
          ],
        },
      },

      '/import-san-pham.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/import-san-pham.ejs',
          get: 'view',
          title: 'Nhập sản phẩm từ file excel',
          upload: [{ name: 'excelFile' }],
          post: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              let categories = await CategoryModel.MODEL.getAllCategory();
              let stores = await ServicesModel.MODEL.getServicesByCondition({
                userId: req.user._id,
                businessTypes: {$in: ['0']},
                status: 1,
              });
              return ChildRouter.renderOrResponse(req, res, {
                categories,
                stores,
              });
            },
          ],

          post: [
            async function (req, res) {
              const fileColumn = [
                'Tên sản phẩm',
                'Hình ảnh',
                'Danh mục',
                'Cửa hàng',
                'Cơ sở',
                'Giá (VNĐ)',
                'Thương hiệu',
                'Vận Chuyển',
                'Tên nhóm',
                'Khối lượng (Kg)',
                'Mô tả',
              ];
              const fieldMapping = {
                'Tên sản phẩm': 'name',
                'Hình ảnh': 'pictures',
                'Danh mục': 'categoryId',
                'Cửa hàng': 'storeId',
                'Cơ sở': 'branchId', // Array
                'Giá (VNĐ)': 'price',
                'Thương hiệu': 'trademark',
                'Vận Chuyển': 'transport',
                'Tên nhóm': 'classify',
                'Khối lượng (Kg)': 'weight',
                'Mô tả': 'description',
              };

              const products = [];

              if (
                !req.files ||
                !req.files.excelFile ||
                !req.files.excelFile.length
              ) {
                return ChildRouter.responseError('Tải file lỗi.', res);
              }
              const file = req.files.excelFile[0];
              const workbook = XLSX.readFile(APP.BASE_DIR + '/' + file.path);
              const sheetNames = workbook.SheetNames;
              if (!sheetNames || !sheetNames.length) {
                return ChildRouter.responseError('Tải file lỗi.', res);
              }
              const firstSheet = workbook.Sheets[sheetNames[0]];
              const objx = XLSX.utils.sheet_to_row_object_array(firstSheet);

              objx.forEach((product) => {
                const newProduct = { userId: req.user._id };
                let listWeight = product['Khối lượng (Kg)']
                  ? product['Khối lượng (Kg)'].toString().split('/')
                  : [];
                const weight = listWeight[0] ? listWeight[0] : 0;

                Object.keys(product).forEach((prodKey) => {
                  if (fieldMapping[prodKey]) {
                    if (fieldMapping[prodKey] === 'pictures') {
                      const pictures = product[prodKey]
                        ? product[prodKey].split(';')
                        : [];
                      const prependedPictures = pictures.map(
                        (picture) => `/files/${req.user._id}/${picture.trim()}`
                      );
                      newProduct[fieldMapping[prodKey]] = prependedPictures;
                      if (prependedPictures && prependedPictures.length) {
                        newProduct['thumbail'] = prependedPictures[0];
                      }
                    }
                    // if (fieldMapping[prodKey] === "name") {
                    //     newProduct['nameUTF'] = product[prodKey];
                    // }
                    // if (fieldMapping[prodKey] === "description") {
                    //     newProduct['descriptionUTF'] = product[prodKey];
                    // }
                    if (fieldMapping[prodKey] === 'classify') {
                      const classifies = product[prodKey]
                        ? product[prodKey].split(';')
                        : [];
                      const classify = classifies.map((item) => {
                        const result = {};
                        if (item) {
                          const classifySegment = item.split('/');
                          result['name'] = (classifySegment[0] + '')
                            .replace('\r', '')
                            .replace('\n', '');
                          result['data'] = [];

                          for (let i = 1; i < classifySegment.length; i++) {
                            if (!classifySegment[i]) {
                              break;
                            }
                            const childClass = classifySegment[i].split('=');
                            if (childClass && childClass.length === 2) {
                              if (childClass[1] && childClass[0]) {
                                let mass = listWeight[i - 1]
                                  ? listWeight[i - 1]
                                  : listWeight[0];

                                result['data'].push({
                                  name: (childClass[0] + '')
                                    .replace('\r', '')
                                    .replace('\n', ''),
                                  price:
                                    NumberUtils.priceFormat(childClass[1]) +
                                    ' VND',
                                  mass: mass,
                                });
                              }
                            }
                          }
                        }
                        return result;
                      });
                      newProduct[fieldMapping[prodKey]] = classify;
                    } else if (fieldMapping[prodKey] === 'weight') {
                      newProduct['weight'] = weight;
                    } else {
                      newProduct[fieldMapping[prodKey]] = product[prodKey];
                    }
                  }
                });
                products.push(newProduct);
              });

              try {
                for (let product of products) {
                  product.nameUTF = product.name
                    ? StringUtils.removeUtf8(product.name)
                    : '';
                  product.descriptionUTF = product.description
                    ? StringUtils.removeUtf8(product.description)
                    : null;
                  const productStoreCode = product.storeId
                    ? product.storeId.replace(/CH/g, '')
                    : '';
                  let productBranchCodes = product.branchId
                    ? product.branchId.replace(/CS/g, '').split(';')
                    : [];
                  let categoryCode = product.categoryId
                    ? product.categoryId.replace(/DM/g, '')
                    : '';
                  productBranchCodes = productBranchCodes.map((branchCode) =>
                    Number.parseInt(branchCode)
                  );

                  if (productStoreCode) {
                    let storeCode = Number.parseInt(productStoreCode);
                    let store =
                      await ServicesModel.MODEL.getServicesByCondition({
                        code: storeCode,
                      });
                    if (store && store.length) {
                      product.storeId = store[0]._id.toString();
                    } else {
                      product.storeId = '';
                    }
                  }

                  if (productBranchCodes && productBranchCodes.length) {
                    let branchs = await BranchModel.MODEL.getBranchByCondition({
                      code: productBranchCodes,
                    });
                    const branchIds = branchs.map((branch) =>
                      branch._id.toString()
                    );
                    product.branchId = branchIds;
                  } else {
                    let branchs = await BranchModel.MODEL.getBranchByCondition(
                      {}
                    );
                    const branchIds = branchs.map((branch) =>
                      branch._id.toString()
                    );
                    product.branchId = branchIds;
                  }

                  product.categoryId = ''; // Default is blank
                  if (categoryCode) {
                    let category =
                      await CategoryModel.MODEL.getCategoryByCondition({
                        code: Number.parseInt(categoryCode),
                        status: 1,
                      });
                    if (category && category[0]) {
                      product.categoryId = category[0]._id.toString();
                    }
                  }
                  if (product.storeId && product.storeId.length === 24) {
                    await ProducModel.MODEL.addProducts(product);
                  }
                }
              } catch (err) {
                console.log(err);
              }

              return ChildRouter.responseSuccess(
                'Sản phẩm sẽ được hiển thị sau khi hệ thống kiểm duyệt nội dung',
                res
              );
            },
          ],
        },
      },

      '/edit-product/:productId.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/chinh-sua-san-pham.ejs',
          get: 'view',
          title: 'Chỉnh sửa sản phẩm',
          upload: [
            { name: 'thumbail', maxCount: 1 },
            { name: 'pictures', maxCount: 15 },
          ],
          post: 'json',
          validate: {
            body: {
              method: ['post'],
              validate: [
                {
                  key: 'name',
                  type: this.dataType.string,
                  name: 'Tên sản phẩm',
                  min: 5,
                  max: 200,
                },
                {
                  key: 'storeId',
                  type: this.dataType.string,
                  name: 'Cửa hàng',
                },
                { key: 'branchId', type: this.dataType.string, name: 'Cơ sở' },
                {
                  key: 'categoryId',
                  type: this.dataType.string,
                  name: 'Danh mục',
                },
                {
                  key: 'description',
                  type: this.dataType.string,
                  name: 'Mô tả',
                  min: 5,
                },
                {
                  key: 'trademark',
                  type: this.dataType.string,
                  name: 'Thương hiệu',
                  min: 2,
                  max: 200,
                },
                {
                  key: 'price',
                  type: this.dataType.number,
                  name: 'Giá',
                  min: 5,
                },
                {
                  key: 'typeProduct',
                  type: this.dataType.number,
                  name: 'Tình trạng',
                },
                {
                  key: 'transport',
                  type: this.dataType.string,
                  name: 'Vận chuyển',
                  min: 2,
                },
                {
                  key: 'weight',
                  type: this.dataType.number,
                  name: 'Khối lượng vận chuyển',
                },
              ],
            },
          },
        },

        methods: {
          get: [
            async function (req, res) {
              let categories = await CategoryModel.MODEL.getCategoryByCondition({type: { $in: [ 0,1,2 ] }});
              let stores = await ServicesModel.MODEL.getServicesByCondition({
                userId: req.user._id,
              });
              let branch = await BranchModel.MODEL.getBranchById(
                req.params.branchId
              );
              let product = await ProducModel.MODEL.getProductsById(
                req.params.productId
              );
              if (product && !product.classify) product.classify = [];
              return ChildRouter.renderOrResponse(req, res, {
                categories,
                product,
                stores,
                branch,
              });
            },
          ],

          post: [
            async function (req, res) {
              let {
                name,
                storeId,
                categoryId,
                branchId,
                description,
                trademark,
                classify,
                price,
                priceOld,
                typeProduct,
                typeShip,
                pictureOlds,
                transport,
                weight,
              } = req.body;
              classify = JSON.parse(classify);

              // fix bug nếu là 1 cơ sở thì client post string cần convert to array
              if(!Array.isArray(branchId)){
                branchId = [branchId]
              }

              let obj = {
                userId: req.user._id,
                name,
                nameUTF: StringUtils.removeUtf8(name),
                descriptionUTF: StringUtils.removeUtf8(description),
                storeId,
                branchId,
                categoryId,
                description,
                trademark,
                price,
                priceOld,
                typeProduct,
                typeShip,
                classify,
                transport,
                weight: Number(weight),
              };
              pictureOlds = JSON.parse(pictureOlds);

              if (categoryId == 'null') {
                return ChildRouter.responseError('Danh mục không phù hợp', res);
              }
              let product = await ProducModel.MODEL.getProductsById(
                req.params.productId
              );
              if (req.upload && req.upload.thumbail) {
                FileUtils.deleteFile(APP.BASE_DIR + product.thumbail);
                obj.thumbail = req.upload.thumbail[0].path;
              }
              if (
                req.upload &&
                req.upload.pictures &&
                req.upload.pictures.length > 0
              ) {
                let pictures = req.upload.pictures;
                pictures.forEach((img, index) => {
                  if (!pictureOlds.includes(img))
                  {
                    pictureOlds.push(img.path);
                  }
                });
              }
              obj.pictures = pictureOlds;

              product.pictures.forEach((item) => {
                if (obj.pictures && !obj.pictures.includes(item)) {
                  FileUtils.deleteFile(APP.BASE_DIR + item);
                }
              });
              const rsUpdate = await ProducModel.MODEL.updateProducts(req.params.productId, obj);
              return ChildRouter.responseSuccess('Thành công', res);
            },
          ],
        },
      },

      '/chinh-sua-thuong-hieu/:serviceId.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/brand-edit.ejs',
          get: 'view',
          title: 'Chỉnh sửa thương hiệu',
          upload: [
            { name: 'thumbnail', maxCount: 1 },
            { name: 'pictures', maxCount: 15 },
          ],
          post: 'json',
          validate: {
            body: {
              method: ['post'],
              validate: [
                {
                  key: 'name',
                  type: this.dataType.string,
                  name: 'Tên thương hiệu',
                  min: 5,
                },
                {
                  key: 'content',
                  type: this.dataType.string,
                  name: 'Giới thiệu',
                },
                {
                  key: 'province',
                  type: this.dataType.string,
                  name: 'Tỉnh / Thành phố',
                },
                {
                  key: 'district',
                  type: this.dataType.string,
                  name: 'Quận / Huyện',
                },
                {
                  key: 'ward',
                  type: this.dataType.string,
                  name: 'Phường / Xã',
                },
                {
                  key: 'businessTypes',
                  type: this.dataType.array,
                  name: 'Loại hình kinh doanh',
                  min: 1,
                },
              ],
            },
          },
        },

        methods: {
          get: [
            async function (req, res) {
              let dataService = await ServicesModel.MODEL.getDataById(
                req.params.serviceId
              );
              if (dataService.userId != req.user._id) {
                return ChildRouter.redirect('/thong-tin-tai-khoan.html');
              }
              return ChildRouter.renderOrResponse(req, res, {
                dataService,
                typeServices: dataService.type,
                dateTimes,
              });
            },
          ],

          post: [
            async function (req, res) {
              let dataService = await ServicesModel.MODEL.getDataById(
                req.params.serviceId
              );
              if (dataService.userId != req.user._id) {
                return ChildRouter.redirect('/thong-tin-tai-khoan.html');
              }
              let {
                name,
                content,
                timeOpen,
                timeClose,
                thumbnail,
                pictureOlds,
                betweenTime,
                province,
                district,
                ward,
                street,
                location,
                isFeatured,
                businessTypes,
              } = req.body;

              location ? '' : (location = '');
              street ? '' : (street = '');

              if (location == '' && street == '') {
                return ChildRouter.responseError(
                  'Đường/Phố hoặc Địa chỉ không được rỗng',
                  res
                );
              }

              let address =
                `${location} ${street}, ${ward}, ${district}, ${province}`.trim();
              let obj = {
                name,
                address,
                nameUTF: StringUtils.removeUtf8(name),
                addressUTF: StringUtils.removeUtf8(address),
                provinceUTF: StringUtils.removeUtf8(province),
                districtUTF: StringUtils.removeUtf8(district),
                content,
                province,
                district,
                ward,
                street,
                location,
                isFeatured,
                businessTypes,
              };

              if (!betweenTime || betweenTime == 'null') {
                return ChildRouter.responseError(
                  'Bạn chưa chọn thời gian phân biệt nửa ngày',
                  res
                );
              }
              obj.betweenTime = betweenTime;

              if (timeOpen == 'null' || timeClose == 'null') {
                return ChildRouter.responseError('Thời gian không hợp lệ', res);
              }
              obj = { ...obj, timeOpen, timeClose };
              pictureOlds = JSON.parse(pictureOlds);

              if (
                req.upload &&
                req.upload.pictures &&
                req.upload.pictures.length > 0
              ) {
                let imageNews = req.upload.pictures;
                imageNews.forEach((img, index) => {
                  if (!pictureOlds.includes(img))
                  {
                    pictureOlds.push(img.path);
                  }
                });
              }

              obj.pictures = pictureOlds;

              dataService.pictures.forEach((item) => {
                if (obj.pictures && !obj.pictures.includes(item)) {
                  FileUtils.deleteFile(APP.BASE_DIR + item);
                }
              });

              if (req.upload && req.upload.thumbnail) {
                obj.thumbnail = req.upload.thumbnail[0].path;
              }
              let rs = await ServicesModel.MODEL.updateServices(
                req.params.serviceId,
                obj
              );
              return ChildRouter.responseSuccess('Thành công', res);
            },
          ],
        },
      },

      '/chi-tiet-don-hang-cho-lay-hang.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/chi-tiet-don-hang-cho-lay-hang.ejs',
          get: 'view',
          title: 'Chi tiết đơn hàng chờ lấy hàng',
        },

        methods: {
          get: [
            async function (req, res) {
              return ChildRouter.renderOrResponse(req, res);
            },
          ],
        },
      },

      '/chi-tiet-don-hang-da-giao.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/chi-tiet-don-hang-da-giao.ejs',
          get: 'view',
          title: 'Chi tiết đơn hàng đã giao',
        },

        methods: {
          get: [
            async function (req, res) {
              return ChildRouter.renderOrResponse(req, res);
            },
          ],
        },
      },

      '/chi-tiet-don-hang-dang-giao.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/chi-tiet-don-hang-dang-giao.ejs',
          get: 'view',
          title: 'Chi tiết đơn hàng đang giao',
        },

        methods: {
          get: [
            async function (req, res) {
              return ChildRouter.renderOrResponse(req, res);
            },
          ],
        },
      },

      '/chinh-sua-lich-hen.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/chinh-sua-lich-hen.ejs',
          get: 'view',
          title: 'Chỉnh sửa lịch hẹn',
        },

        methods: {
          get: [
            async function (req, res) {
              return ChildRouter.renderOrResponse(req, res);
            },
          ],
        },
      },

      '/quan-ly-doanh-thu.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/quan-ly-doanh-thu.ejs',
          get: 'view',
          title: 'Quản lý doanh thu',
        },

        methods: {
          get: [
            async function (req, res) {
              let admins = await UserModel.MODEL.getDataWhere(
                { type: 0 },
                UserModel.MODEL.FIND_MANY()
              );
              let adminIds = admins.map((a) => {
                return a._id.toString();
              });
              let banks = await AccountBankModel.MODEL.getBankByCondition({
                userId: { $in: adminIds },
              });
              let huongDan = await HuongDanNapTienModel.MODEL.getData();

              return ChildRouter.renderOrResponse(req, res, {
                banks,
                huongDan,
              });
            },
          ],
        },
      },

      '/lay-thong-tin-phi.html': {
        config: {
          auth: [this.roles.store],
          post: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              let fundHistories =
                await FundLogModel.MODEL.getFundLogByCondition({
                  userId: req.user._id,
                });
              let mapFunc = fundHistories.map((item) => {
                return new promise(async (resolve) => {
                  switch (item.typeService) {
                    case 0:
                      let request =
                        await RequestBuyProductModel.MODEL.getRequestByIdNoData(
                          item.requestId
                        );
                      item.code = request.code;
                      break;
                    case 1:
                      let bookExam =
                        await BookExaminationModel.MODEL.getBookExaminationByIdNoData(
                          item.bookId
                        );
                      item.code = bookExam.code;
                      break;
                    case 2:
                      let bookRoom =
                        await BookRoomModel.MODEL.getBookRoomByIdNoData(
                          item.bookId
                        );
                      item.code = bookRoom.code;
                      break;
                    case 3:
                      let bookSpa =
                        await BookSpaModel.MODEL.getBookSpayIdNoData(
                          item.bookId
                        );
                      item.code = bookSpa.code;
                      break;
                  }
                  return resolve();
                });
              });

              if (mapFunc.length > 0) await promise.all(mapFunc);
              let fundDay = 0;
              let funYesterday = 0;
              let funWeek = 0;
              let funMonth = 0;
              let funAll = 0;
              fundHistories.forEach((item) => {
                let timeDay = 86400000;
                let timeToDay = new Date().setHours(0, 0, 0, 0);
                let timeDayNow = new Date().getTime();
                if (item.createAt >= timeToDay && item.createAt < timeDayNow) {
                  fundDay += item.fee;
                }
                if (
                  item.createAt >= timeToDay - timeDay &&
                  item.createAt < timeToDay
                ) {
                  funYesterday += item.fee;
                }
                let dayWeek = new Date().getDay();
                if (dayWeek == 0) {
                  dayWeek = 7;
                }
                if (
                  item.createAt >= timeToDay - (dayWeek - 1) * timeDay &&
                  item.createAt < timeDayNow
                ) {
                  funWeek += item.fee;
                }
                let dayMonth = new Date().getDate();
                if (
                  item.createAt >= timeToDay - (dayMonth - 1) * timeDay &&
                  item.createAt < timeDayNow
                ) {
                  funMonth += item.fee;
                }
                funAll += item.fee;
              });
              return ChildRouter.responseSuccess('Thành công', res, {
                fundHistories,
                fundDay,
                funYesterday,
                funWeek,
                funMonth,
                funAll,
              });
            },
          ],
        },
      },

      '/lay-thong-thu-nhap.html': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              let services = await ServicesModel.MODEL.getServicesByCondition({
                userId: req.user._id,
                businessTypes: {$in: ['1','2','3']},
              });
              let listServiceBytype = {
                1: [],
                2: [],
                3: [],
              };
              services.forEach((item) => {
                listServiceBytype[item.type].push(item._id.toString());
                listServiceBytype[item._id] = item;
              });

              let funcs = [];
              let bookExams = [],
                bookRooms = [],
                bookSpas = [],
                requests = [];
              funcs.push(
                new promise(async (resolve) => {
                  bookExams =
                    await BookExaminationModel.MODEL.getBookExaminationByConditionDetail(
                      {
                        storeId: { $in: listServiceBytype[1] },
                        status: 3,
                      },
                      { timeCheckIn: -1 }
                    );
                  return resolve();
                })
              );

              funcs.push(
                new promise(async (resolve) => {
                  bookRooms =
                    await BookRoomModel.MODEL.getBookRoomByConditionDetail(
                      {
                        storeId: { $in: listServiceBytype[2] },
                        status: 3,
                      },
                      { timeCheckIn: -1 }
                    );
                  return resolve();
                })
              );

              funcs.push(
                new promise(async (resolve) => {
                  bookSpas =
                    await BookSpaModel.MODEL.getBookSpaByConditionDetail(
                      {
                        storeId: { $in: listServiceBytype[3] },
                        status: 3,
                      },
                      { timeCheckIn: -1 }
                    );
                  return resolve();
                })
              );

              funcs.push(
                new promise(async (resolve) => {
                  requests =
                    await RequestBuyProductModel.MODEL.getRequestByCondition({
                      status: ProductOrderStatus.COMPLETED,
                      storeUserId: req.user._id,
                    });
                  return resolve();
                })
              );

              await promise.all(funcs);

              let thuNhaps = [];
              bookExams.forEach((item) => {
                thuNhaps.push({
                  code: item.code,
                  price: item.price,
                  type: 1,
                  timeCheckIn: item.timeCheckIn,
                  id: item._id,
                });
              });
              bookRooms.forEach((item) => {
                thuNhaps.push({
                  code: item.code,
                  price: item.price,
                  type: 2,
                  timeCheckIn: item.timeCheckIn,
                  id: item._id,
                });
              });
              bookSpas.forEach((item) => {
                thuNhaps.push({
                  code: item.code,
                  price: item.price,
                  type: 3,
                  timeCheckIn: item.timeCheckIn,
                  id: item._id,
                });
              });
              requests.forEach((item) => {
                let totalMoney = 0;
                item.products.forEach((item) => {
                  totalMoney += Number(item.count) * Number(item.price);
                });
                thuNhaps.push({
                  code: item.code,
                  price: totalMoney,
                  type: 0,
                  timeCheckIn: item.createAt,
                  id: item._id,
                });
              });

              let fundDay = 0;
              let funYesterday = 0;
              let funWeek = 0;
              let funMonth = 0;
              let funAll = 0;
              thuNhaps.forEach((item) => {
                let timeDay = 86400000;
                let timeToDay = new Date().setHours(0, 0, 0, 0);
                let timeDayNow = new Date().getTime();
                if (
                  item.timeCheckIn >= timeToDay &&
                  item.timeCheckIn < timeDayNow
                ) {
                  fundDay += item.price;
                }
                if (
                  item.timeCheckIn >= timeToDay - timeDay &&
                  item.timeCheckIn < timeToDay
                ) {
                  funYesterday += item.price;
                }
                let dayWeek = new Date().getDay();
                if (dayWeek == 0) {
                  dayWeek = 7;
                }
                if (
                  item.timeCheckIn >= timeToDay - (dayWeek - 1) * timeDay &&
                  item.timeCheckIn < timeDayNow
                ) {
                  funWeek += item.price;
                }
                let dayMonth = new Date().getDate();
                if (
                  item.timeCheckIn >= timeToDay - (dayMonth - 1) * timeDay &&
                  item.timeCheckIn < timeDayNow
                ) {
                  funMonth += item.price;
                }
                funAll += item.price;
              });
              return ChildRouter.responseSuccess('Thành công', res, {
                thuNhaps,
                fundDay,
                funYesterday,
                funWeek,
                funMonth,
                funAll,
              });
            },
          ],
        },
      },

      '/lay-thong-tin-nap-tien.html': {
        config: {
          auth: [this.roles.store],
          post: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              let recharges = await RechargeModel.MODEL.getRechargeByCondition({
                userId: req.user._id,
              });
              return ChildRouter.responseSuccess('Thành công', res, {
                recharges,
              });
            },
          ],
        },
      },

      '/phi-dich-vu.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/phi-dich-vu.ejs',
          get: 'view',
          title: 'Phí dịch vụ',
        },

        methods: {
          get: [
            async function (req, res) {
              let { percent, fee } =
                await SettingAdminModel.MODEL.getSettingAdmin();

              return ChildRouter.renderOrResponse(req, res, { percent, fee });
            },
          ],
        },
      },

      '/chinh-sua-don-hang/:id.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/chi-tiet-don-hang.ejs',
          get: 'view',
          title: 'Chi tiết đơn hàng',
        },

        methods: {
          get: [
            async function (req, res) {
              let requestBuyId = req.params.id;
              let info = await RequestBuyProductModel.MODEL.getRequestById(
                requestBuyId
              );
              if (!info)
                return ChildRouter.redirect(
                  res,
                  '/store/quan-ly-don-hang.html'
                );

              let coupon = null;

              let condition = {
                code: info.code,
                status: 1,
              };
              coupon = await CouponModel.MODEL.getDataWhere(
                condition,
                CouponModel.MODEL.FIND_ONE(),
                { createAt: -1 }
              );

              if (!coupon) {
                coupon = { value: 0, type: 2 };
              }

              if (Number(info.status) == 5) {
                if (info.adminConfirm == 1) {
                  info.messageReject = 'Khách hàng không nhận hàng';
                } else {
                  let notify = await NotificationModel.MODEL.getDataWhere(
                    {
                      requestId: requestBuyId,
                      status: { $in: [3, 6] },
                    },
                    NotificationModel.MODEL.FIND_ONE()
                  );
                  if (notify) {
                    info.messageReject = notify.message;
                  } else {
                    info.messageReject = info.reasonCancel || 'Không rõ';
                  }
                }
              }
              let branches = await BranchModel.MODEL.getBranchByCondition({
                status: { $ne: 2 },
                storeId: info.storeId,
              });
              return ChildRouter.renderOrResponse(req, res, {
                buyInfo: info,
                coupon,
                branches
              });
            },
          ],
        },
      },
      '/huy-don-van.html': {
        config: {
          auth: [this.roles.store],
          post: 'json',
        },

        methods: {
          post: [
            async function (req, res) {
              let { buyId } = req.body;
              let info = await RequestBuyProductModel.MODEL.getRequestById(
                buyId
              );
              if (!info)
                return ChildRouter.responseError('Đơn hàng không tồn tại', res);

              switch (info.shippingService) {
                case 'ghtk':
                  let responseHuyDonHang = await GHTK.huyDonHang(buyId);
                  if (!responseHuyDonHang.success) {
                    return ChildRouter.responseError(
                      responseHuyDonHang.message,
                      res
                    );
                  }

                  await RequestBuyProductModel.MODEL.updateById(buyId, {
                    shippingCode: '',
                    status: 1,
                    transportOrder: null,
                  });
                  return ChildRouter.responseSuccess(
                    'Hủy dịch vụ thành công',
                    res
                  );
                  break;
                default:
                  return ChildRouter.responseError(
                    responseHuyDonHang.message,
                    res
                  );
                  break;
              }
            },
          ],
        },
      },

      '/tinh-phi-van-chuyen.html/:dvvc/:buyId': {
        config: {
          // auth: [],
          auth: [this.roles.all],
          get: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              let { buyId, dvvc } = req.params;
              let info = await RequestBuyProductModel.MODEL.getRequestById(
                buyId
              );
              if (!info)
                return ChildRouter.responseError('Đơn hàng không tồn tại', res);

              switch (dvvc) {
                case 'ghtk':
                  let response = await GHTK.tinhPhiVanChuyen(buyId);
                  return ChildRouter.responseSuccess('Thành công', res, {
                    fee: response.fee,
                  });
                  break;
                default:
                  return ChildRouter.responseError(
                    'Đơn vị vận chuyển không hợp lệ',
                    res
                  );
                  break;
              }
            },
          ],
        },
      },
      '/quan-ly-don-hang.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/quan-ly-don-hang.ejs',
          get: 'view',
          title: 'Quản lý đơn hàng',
        },

        methods: {
          get: [
            async function (req, res) {
              return ChildRouter.renderOrResponse(req, res);
            },
          ],
        },
      },

      '/in-nhan-van-chuyen.html/:buyId': {
        config: {
          auth: [this.roles.store],
          view: 'pages/nhan-van-chuyen.ejs',
          get: 'view',
          title: 'Nhãn vận chuyển',
        },

        methods: {
          get: [
            async function (req, res) {
              let info = await RequestBuyProductModel.MODEL.getRequestById(
                req.params.buyId
              );
              if (!info)
                return ChildRouter.redirect(
                  res,
                  '/store/quan-ly-don-hang.html'
                );
              if (info.transportSystem == 0)
                return ChildRouter.redirect(
                  res,
                  '/store/quan-ly-don-hang.html'
                );
              switch (info.dvVanChuyen) {
                case 'Giao hàng tiết kiệm':
                  let pdfPath = await GHTK.inNhanDonHang(req.params.buyId);
                  return ChildRouter.renderToView(req, res, { pdfPath });
                  break;
                default:
                  return ChildRouter.redirect(
                    res,
                    '/store/quan-ly-don-hang.html'
                  );
                  break;
              }
            },
          ],
        },
      },

      '/lay-danh-sach-don-hang.html': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },

        methods: {
          post: [
            async function (req, res) {
              let { page, search, status, payments, timeStart, timeEnd } =
                req.body;
              if (!page) page = 1;
              let limit = 10;
              let codition = {};
              if (search != '') {
                codition = {
                  $or: [
                    { userName: new RegExp(search, 'i') },
                    { 'products.name': new RegExp(search, 'i') },
                    { codeString: new RegExp(search, 'i') },
                  ],
                };
              }
              codition.createAt = {
                $gte: Number(timeStart),
                $lte: Number(timeEnd),
              };

              codition.storeUserId = req.user._id;
              if (status && status != 'all') {
                codition.status = Number(status);
              }
              // if (payments && payments != 'all') {
              //     codition.payments = Number(payments)
              // }
              let bills = await RequestBuyProductModel.MODEL.getRequestForPage(
                codition,
                page,
                limit
              );

              let countBill =
                await RequestBuyProductModel.MODEL.getCountRequestForPage(
                  codition
                );
              bills.forEach((bill) => {
                bill.listProduct = '';
                bill.totalMoney = 0;
                bill.products.forEach((item) => {
                  bill.listProduct += item.name + '\n';
                  bill.totalMoney += Number(item.price) * Number(item.count);
                });
              });
              return ChildRouter.responseSuccess('Thanh Cong', res, {
                bills,
                countBill,
              });
            },
          ],
        },
      },

      '/delete-bill/:billId.html': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              let { index } = req.query;
              index = Number(index);
              let dataBook = await RequestBuyProductModel.MODEL.getRequestById(
                req.params.billId
              );
              if (!dataBook) {
                return ChildRouter.responseError('Thông tin không hợp lệ', res);
              }
              if (dataBook.storeId != req.user._id) {
                return ChildRouter.responseError('Vượt quyền truy cập', res);
              }
              await RequestBuyProductModel.MODEL.deleteRequests(
                req.params.billId
              );
              return ChildRouter.responseSuccess('Thành công', res);
            },
          ],
        },
      },

      '/quan-ly-lich-hen.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/quan-ly-lich-hen.ejs',
          get: 'view',
          title: 'Quản lý lịch hẹn',
        },

        methods: {
          get: [
            async function (req, res) {
              let services = await ServicesModel.MODEL.getServicesByCondition({
                userId: req.user._id,
              });
              let serviceIds = [];
              services.forEach((item) => {
                serviceIds.push(item._id.toString());
              });
              // book xưởng dịch vụ
              let bookExams =
                await BookExaminationModel.MODEL.getBookExaminationByConditionDetail(
                  { storeId: { $in: serviceIds } },
                  { createAt: -1 }
                );
              let bookRooms =
                await BookRoomModel.MODEL.getBookRoomByConditionDetail(
                  { storeId: { $in: serviceIds } },
                  { createAt: -1 }
                );
              let bookSpas =
                await BookSpaModel.MODEL.getBookSpaByConditionDetail(
                  { storeId: { $in: serviceIds } },
                  { createAt: -1 }
                );
              let bookClassification =
                  await BookClassificationModel.MODEL.getByConditionDetail(
                      { storeId: { $in: serviceIds } },
                      { createAt: -1 }
                  );
              return ChildRouter.renderOrResponse(req, res, {
                bookExams,
                bookRooms,
                bookSpas,
                bookClassification
              });
            },
          ],
        },
      },

      '/change-status-schedule/:scheduleId.html': {
        config: {
          auth: [this.roles.store],
          post: 'json',
        },

        methods: {
          post: [
            async function (req, res) {
              let { status, index } = req.query;
              let { message } = req.body;
              status = Number(status);
              index = Number(index);
              if (status == 2 && message == '') {
                return ChildRouter.responseError('Lý do không hợp lệ', res);
              }

              message = htmlspecialchars(message || '');
              if ([0, 1, 2].includes(status) && [1, 2, 3].includes(index)) {
                let booking, service;
                if (index == 1) {
                  booking =
                    await BookExaminationModel.MODEL.getBookExaminationById(
                      req.params.scheduleId
                    );
                  service = await ServicesModel.MODEL.getServicesById(
                    booking.storeId
                  );
                  if (
                    service.userId != req.user._id ||
                    (booking.status == 1 && Number(status) != 3)
                  ) {
                    return ChildRouter.responseError(
                      'Vượt quyền truy cập',
                      res
                    );
                  }
                  await BookExaminationModel.MODEL.updateBookExamination(
                    req.params.scheduleId,
                    { status: status }
                  );
                } else if (index == 2) {
                  booking = await BookRoomModel.MODEL.getBookRoomById(
                    req.params.scheduleId
                  );
                  service = await ServicesModel.MODEL.getServicesById(
                    booking.storeId
                  );
                  if (
                    service.userId != req.user._id ||
                    (booking.status == 1 && Number(status) != 3)
                  ) {
                    return ChildRouter.responseError(
                      'Vượt quyền truy cập',
                      res
                    );
                  }
                  await BookRoomModel.MODEL.updateBookRoom(
                    req.params.scheduleId,
                    { status: status }
                  );
                } else if (index == 3) {
                  booking = await BookSpaModel.MODEL.getBookSpaById(
                    req.params.scheduleId
                  );
                  service = await ServicesModel.MODEL.getServicesById(
                    booking.storeId
                  );
                  // console.log('booking',booking);
                  if (service.userId != req.user._id || booking.status == 2) {
                    return ChildRouter.responseError(
                      'Vượt quyền truy cập',
                      res
                    );
                  }
                  await BookSpaModel.MODEL.updateBookSpa(
                    req.params.scheduleId,
                    { status: status }
                  );
                }
                if (status == 2 && booking) {
                  NotificationModel.MODEL.addNewNotification(
                    booking.userId,
                    req.user._id,
                    'từ chối lịch hẹn',
                    5,
                    io,
                    {
                      message,
                      typeService: index,
                      bookId: req.params.scheduleId,
                      orderId: booking.orderId,
                    }
                  );
                } else if (status == 1 && booking) {
                  NotificationModel.MODEL.addNewNotification(
                    booking.userId,
                    req.user._id,
                    'chấp nhận lịch hẹn',
                    4,
                    io,
                    {
                      typeService: index,
                      bookId: req.params.scheduleId,
                      orderId: booking.orderId,
                    }
                  );
                }
                return ChildRouter.responseSuccess('Thành công', res);
              } else {
                return ChildRouter.responseError('Thông tin không hợp lệ', res);
              }
            },
          ],
        },
      },

      '/change-status-schedule-done/:scheduleId.html': {
        config: {
          auth: [this.roles.store],
          post: 'json',
          validate: {
            body: {
              method: ['post'],
              validate: [
                {
                  key: 'priceAddition',
                  type: this.dataType.number,
                  name: 'Phụ thu không được để trống. Nếu không có thì nhập 0 VND',
                },
              ],
            },
          },
        },

        methods: {
          post: [
            async function (req, res) {
              let { index } = req.query;
              let { priceAddition } = req.body;
              index = Number(index);
              let result = null;
              let priceBook = 0;

              if ([1, 2, 3].includes(index)) {
                let bookRoom, service;
                if (index == 1) {
                  bookRoom =
                    await BookExaminationModel.MODEL.getBookExaminationById(
                      req.params.scheduleId
                    );
                  if (bookRoom.status == 3)
                    return ChildRouter.responseError(
                      'Dịch vụ đã được xác nhận hoàn thành',
                      res
                    );
                  if (bookRoom.status != 1)
                    return ChildRouter.responseError(
                      'Dịch vụ đã bị huỷ trước đó. Vui lòng kiểm tra lại trang thái dịch vụ',
                      res
                    );
                  service = await ServicesModel.MODEL.getServicesById(
                    bookRoom.storeId
                  );
                  if (service.userId != req.user._id) {
                    return ChildRouter.responseError(
                      'Vượt quyền truy cập',
                      res
                    );
                  }
                  result =
                    await BookExaminationModel.MODEL.updateBookExamination(
                      req.params.scheduleId,
                      {
                        status: 3,
                        priceAddition,
                      }
                    );
                } else if (index == 2) {
                  bookRoom = await BookRoomModel.MODEL.getBookRoomById(
                    req.params.scheduleId
                  );
                  if (bookRoom.status == 3)
                    return ChildRouter.responseError(
                      'Dịch vụ đã được xác nhận hoàn thành',
                      res
                    );
                  if (bookRoom.status != 1)
                    return ChildRouter.responseError(
                      'Dịch vụ đã bị huỷ trước đó. Vui lòng kiểm tra lại trang thái dịch vụ',
                      res
                    );
                  service = await ServicesModel.MODEL.getServicesById(
                    bookRoom.storeId
                  );
                  if (service.userId != req.user._id) {
                    return ChildRouter.responseError(
                      'Vượt quyền truy cập',
                      res
                    );
                  }
                  result = await BookRoomModel.MODEL.updateBookRoom(
                    req.params.scheduleId,
                    {
                      status: 3,
                      priceAddition,
                    }
                  );
                } else if (index == 3) {
                  bookRoom = await BookSpaModel.MODEL.getBookSpaById(
                    req.params.scheduleId
                  );

                  if (bookRoom.status == 3)
                    return ChildRouter.responseError(
                      'Dịch vụ đã được xác nhận hoàn thành',
                      res
                    );
                  if (bookRoom.status != 1)
                    return ChildRouter.responseError(
                      'Dịch vụ đã bị huỷ trước đó. Vui lòng kiểm tra lại trang thái dịch vụ',
                      res
                    );
                  service = await ServicesModel.MODEL.getServicesById(
                    bookRoom.storeId
                  );
                  if (service.userId != req.user._id) {
                    return ChildRouter.responseError(
                      'Vượt quyền truy cập',
                      res
                    );
                  }
                  if (
                    bookRoom.timeCheckIn &&
                    bookRoom.timeCheckIn > new Date().getTime()
                  ) {
                    return ChildRouter.responseError(
                      'Bạn chỉ có thể hoàn thành sau thời gian làm dịch vụ!',
                      res
                    );
                  }
                  result = await BookSpaModel.MODEL.updateBookSpa(
                    req.params.scheduleId,
                    {
                      status: 3,
                      priceAddition,
                    }
                  );
                }

                priceBook = !bookRoom.price ? 0 : bookRoom.price;

                // Cập nhật tích điểm vào bảng Book tương ứng.
                if (result) {
                  if (priceBook > 0) {
                    const pointInsert = PointUtil.MoneyToPoint(priceBook);
                    PointUtil.UpdatePointOrder(
                      bookRoom.orderId,
                      pointInsert,
                      index
                    ); // Cập nhật vào bảng book
                    PointUtil.UpdateUserPoint(bookRoom.userId, bookRoom.orderId, pointInsert, index); // Cập nhật User Point
                  }
                }
                // End

                if (req.user.fee == 1) {
                  let settingAd =
                    await SettingAdminModel.MODEL.getSettingAdmin();
                  await UserModel.MODEL.updateUser(req.user._id, {
                    $inc: { funds: -Number(settingAd.fee) },
                  });
                  await FundLogModel.MODEL.addFundLog({
                    userId: req.user._id,
                    storeId: bookRoom.storeId,
                    bookId: req.params.scheduleId,
                    fee: settingAd.fee,
                    typeService: index,
                  });
                }
                return ChildRouter.responseSuccess('Thành công', res);
              } else {
                return ChildRouter.responseError('Thông tin không hợp lệ', res);
              }
            },
          ],
        },
      },
      '/delete-schedule/:scheduleId.html': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              let { index } = req.query;
              index = Number(index);
              if ([1, 2, 3].includes(index)) {
                let dataBook = null;
                if (index == 1) {
                  dataBook =
                    await BookExaminationModel.MODEL.getBookExaminationById(
                      req.params.scheduleId
                    );
                } else if (index == 2) {
                  dataBook = await BookRoomModel.MODEL.getBookRoomById(
                    req.params.scheduleId
                  );
                } else if (index == 3) {
                  dataBook = await BookSpaModel.MODEL.getBookSpaById(
                    req.params.scheduleId
                  );
                }
                if (!dataBook) {
                  return ChildRouter.responseError(
                    'Thông tin không hợp lệ',
                    res
                  );
                }
                let service = await ServicesModel.MODEL.getServicesById(
                  dataBook.storeId
                );
                if (service.userId != req.user._id) {
                  return ChildRouter.responseError('Vượt quyền truy cập', res);
                }
                if (index == 1) {
                  await BookExaminationModel.MODEL.deleteBookExamination(
                    req.params.scheduleId
                  );
                } else if (index == 2) {
                  await BookRoomModel.MODEL.deleteBookRoom(
                    req.params.scheduleId
                  );
                } else if (index == 3) {
                  await BookSpaModel.MODEL.deleteBookSpa(req.params.scheduleId);
                }
                return ChildRouter.responseSuccess('Thành công', res);
              } else {
                return ChildRouter.responseError('Thông tin không hợp lệ', res);
              }
            },
          ],
        },
      },

      '/chi-tiet-lich-hen/:scheduleId.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/chi-tiet-lich-hen.ejs',
          get: 'view',
          title: 'Chi tiết lịch hẹn',
        },

        methods: {
          get: [
            async function (req, res) {
              let { index } = req.query;
              index = Number(index);
              if ([1, 2, 3, 4].includes(index)) {
                let dataBook = null;
                if (index == 1) {
                  dataBook =
                    await BookExaminationModel.MODEL.getBookExaminationById(
                      req.params.scheduleId
                    );
                } else if (index == 2) {
                  dataBook = await BookRoomModel.MODEL.getBookRoomById(
                    req.params.scheduleId
                  );
                } else if (index == 3) {
                  dataBook = await BookSpaModel.MODEL.getBookSpaById(
                    req.params.scheduleId
                  );
                } else if (index == 4) {
                  dataBook = await BookClassificationModel.MODEL.getOneById(
                      req.params.scheduleId
                  );
                }
                if (!dataBook) {
                  return ChildRouter.redirect(
                    res,
                    '/store/quan-ly-lich-hen.html'
                  );
                }
                let service = await ServicesModel.MODEL.getServicesById(
                  dataBook.storeId
                );
                if (service.userId != req.user._id) {
                  return ChildRouter.responseError('Vượt quyền truy cập', res);
                }
                dataBook.storeName = service.name;
                dataBook.storeAddress = service.address;
                dataBook.type = index;

                if (Number(dataBook.status) == 2) {
                  let notify = await NotificationModel.MODEL.getDataWhere(
                    {
                      bookId: req.params.scheduleId,
                      type: { $in: [5, 7] },
                    },
                    NotificationModel.MODEL.FIND_ONE()
                  );
                  if (notify) {
                    dataBook.messageReject = notify.message;
                  } else {
                    dataBook.messageReject = 'Không xác định';
                  }
                }
                return ChildRouter.renderOrResponse(req, res, { dataBook });
              } else {
                return ChildRouter.redirect(
                  res,
                  '/store/quan-ly-lich-hen.html'
                );
              }
            },
          ],
        },
      },

      '/quan-ly-tin-nhan.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/quan-ly-tin-nhan.ejs',
          get: 'view',
          title: 'Quản lý tin nhắn',
        },

        methods: {
          get: [
            async function (req, res) {
              let userId = req.user._id
              let user = await UserModel.MODEL.getUsersById(userId);
              let userSetting = await UserSettingModel.MODEL.getSetting(userId);
              //
              // if (user && user.type == 2) {
              //   let message = await MessageModel.MODEL.getOneMessageByCondition(
              //     { $or: [{ userId: userId }, { userSendId: userId }] }
              //   );
              //   if (!message && userSetting.nhanTinNhanTuNguoiKhac == 1) {
              //     await MessageModel.MODEL.addMessage({
              //       userId,
              //       userSendId: req.user._id,
              //       content: 'Xin chào',
              //       type: 0,
              //     });
              //   }
              // } else {
              //   userId = 'null';
              // }
              return ChildRouter.renderOrResponse(req, res, {
                userChatId: userId,
                userLoginId: userId
              });
            },
          ],
        },
      },

      '/kiem-tra-trang-thai-tin-nhan-user.html/:userId': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              let { userId } = req.params;
              let userSetting = await UserSettingModel.MODEL.getSetting(userId);
              return ChildRouter.responseSuccess('Thanh cong', res, {
                nhanTinNhanTuNguoiKhac: userSetting.nhanTinNhanTuNguoiKhac,
              });
            },
          ],
        },
      },

      '/lay-du-lieu-tin-nhan.html': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              let codition = {
                $or: [{ userId: req.user._id }, { userSendId: req.user._id }],
              };
              let messages = await MessageModel.MODEL.getMessageByCondition(
                codition,
                { createAt: -1 }
              );
              let userIds = [];
              messages.forEach((item) => {
                if (
                  !userIds.includes(item.userId) &&
                  item.userId != req.user._id
                ) {
                  userIds.push(item.userId);
                }
                if (
                  !userIds.includes(item.userSendId) &&
                  item.userSendId != req.user._id
                ) {
                  userIds.push(item.userSendId);
                }
              });
              let users = await UserModel.MODEL.getUsersInIds(userIds);
              let dataUser = {};
              let dataService = {};
              let listUsers = [];
              users.forEach((item) => {
                dataUser[item._id] = item;
              });
              messages.forEach((item) => {
                if (item.userId != req.user._id) {
                  item.fullName = dataUser[item.userId].fullName;
                  item.userPicture = dataUser[item.userId].picture;
                  if (!dataService[item.userId]) {
                    dataService[item.userId] = true;
                    listUsers.push(item);
                  }
                } else {
                  item.fullName = dataUser[item.userSendId].fullName;
                  item.userPicture = dataUser[item.userSendId].picture;
                  if (!dataService[item.userSendId]) {
                    dataService[item.userSendId] = true;
                    listUsers.push(item);
                  }
                }
              });
              return ChildRouter.responseSuccess('Thành công', res, {
                messages,
                listUsers,
              });
            },
          ],
        },
      },

      '/gui-tin-nhan-anh.html/:to': {
        config: {
          auth: [this.roles.store],
          post: 'json',
          upload: [{ name: 'picture', maxCount: 10 }],
        },

        methods: {
          post: [
            async function (req, res) {
              let { to } = req.params;
              // Kiểm tra trạng thái mở tin nhắn
              let setting = await UserSettingModel.MODEL.getSetting(to);
              if (setting.nhanTinNhanTuNguoiKhac == 0)
                return ChildRouter.responseError(
                  'Người dùng này đã tắt nhận tin nhắn',
                  res
                );

              function insertMessage(picture) {
                return new Promise(async (resolve) => {
                  let chat = await MessageModel.MODEL.addMessage({
                    userId: to,
                    userSendId: req.user._id,
                    content: 'Đã gửi ảnh',
                    picture: picture.path,
                    height: picture.height,
                    width: picture.width,
                    type: 1,
                  });
                  chat.fullName = req.user.fullName;
                  chat.userPicture = req.user.picture;
                  let data = { chat, type: 'new-message' };
                  setTimeout(async () => {
                    await UserModel.MODEL.updateUser(to, {
                      $inc: { countMessage: 1 },
                    });
                    let newInfo = await UserModel.MODEL.getUsersById(to);
                    sendHasMessageNotification(to, req.user._id);
                    io.emit(to, {
                      type: 'change-count-message',
                      countMessage: newInfo.countMessage,
                    });
                  }, 500);
                  io.emit(to, data);
                  io.emit(req.user._id, data);
                  setTimeout(() => {
                    return resolve();
                  }, 800);
                });
              }

              if (req.upload && req.upload.picture) {
                req.upload.picture.forEach(async (p) => {
                  await insertMessage(p);
                });
                return ChildRouter.responseSuccess('Thành công', res);
              } else {
                return ChildRouter.responseError('Ảnh không hợp lệ', res);
              }
            },
          ],
        },
      },

      '/them-moi-lich-hen.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/them-moi-lich-hen.ejs',
          get: 'view',
          title: 'Thêm mới lịch hẹn',
        },

        methods: {
          get: [
            async function (req, res) {
              return ChildRouter.renderOrResponse(req, res);
            },
          ],
        },
      },

      '/khach-san.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/dich-vu.ejs',
          get: 'view',
          title: 'Điểm gửi xe',
        },

        methods: {
          get: [
            async function (req, res) {
              if (req.user.servicesParking != 0) {
                return ChildRouter.redirect(
                  res,
                  '/store/quan-ly-doanh-thu.html'
                );
              }
              let services = await ServicesModel.MODEL.getServicesByCondition({
                businessTypes: {$in: ['2']},
                status: { $ne: 2 },
                userId: req.user._id,
              });
              return ChildRouter.renderOrResponse(req, res, {
                services,
                typeServices: 2,
                checkBank: 1,
              });
            },
          ],
        },
      },

      '/cua-hang.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/dich-vu.ejs',
          get: 'view',
          title: 'Cửa hàng',
        },

        methods: {
          get: [
            async function (req, res) {
              if (req.user.servicesStore != 0) {
                return ChildRouter.redirect(
                  res,
                  '/store/quan-ly-doanh-thu.html'
                );
              }
              let banks = await AccountBankModel.MODEL.getBankByCondition({
                userId: req.user._id,
              });
              let services = await ServicesModel.MODEL.getServicesByCondition({
                businessTypes: {$in: ['0']},
                status: { $ne: 2 },
                userId: req.user._id,
              });
              return ChildRouter.renderOrResponse(req, res, {
                services,
                typeServices: 0,
                checkBank: banks.length > 0 ? 1 : 0,
              });
            },
          ],
        },
      },
      // Thuong Hieu
      '/thuong-hieu.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/brand-list.ejs',
          get: 'view',
          title: 'Quản lý thương hiệu',
        },

        methods: {
          get: [
            async function (req, res) {
              // if (req.user.servicesSpa != 0) {
              //     return ChildRouter.redirect(res,'/store/quan-ly-doanh-thu.html')
              // }
              let services = await ServicesModel.MODEL.getServicesByCondition({
                status: { $ne: 2 },
                userId: req.user._id,
              });
              return ChildRouter.renderOrResponse(req, res, {
                services,
                checkBank: 1,
              });
            },
          ],
        },
      },
      // END THuong Hieu

      '/them-thuong-hieu.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/brand-add.ejs',
          get: 'view',
          title: 'Thêm thương hiệu',
          upload: [{ name: 'thumbnail', maxCount: 1 }, { name: 'pictures' }],
          post: 'json',
          validate: {
            body: {
              method: ['post'],
              validate: [
                {
                  key: 'name',
                  type: this.dataType.string,
                  name: 'Tên thương hiệu',
                  min: 5,
                },
                {
                  key: 'content',
                  type: this.dataType.string,
                  name: 'Giới thiệu',
                },
                {
                  key: 'province',
                  type: this.dataType.string,
                  name: 'Tỉnh / Thành phố',
                },
                {
                  key: 'district',
                  type: this.dataType.string,
                  name: 'Quận / Huyện',
                },
                {
                  key: 'ward',
                  type: this.dataType.string,
                  name: 'Phường / Xã',
                },
                {
                  key: 'businessTypes',
                  type: this.dataType.array,
                  name: 'Loại hình kinh doanh',
                  min: 1,
                },
              ],
            },
          },
        },

        methods: {
          get: [
            async function (req, res) {
              return ChildRouter.renderOrResponse(req, res, {
                dateTimes,
              });
            },
          ],

          post: [
            async function (req, res) {
              let {
                name,
                content,
                timeOpen,
                timeClose,
                thumbnail,
                betweenTime,
                province,
                district,
                ward,
                street,
                location,
                businessTypes,
              } = req.body;
              location ? '' : (location = '');
              street ? '' : (street = '');
              if (location == '' && street == '') {
                return ChildRouter.responseError(
                  'Đường/Phố hoặc Địa chỉ không được rỗng',
                  res
                );
              }
              let address =
                `${location}, ${street}, ${ward}, ${district}, ${province}`.trim();
              let obj = {
                userId: req.user._id,
                name,
                address,
                nameUTF: StringUtils.removeUtf8(name),
                addressUTF: StringUtils.removeUtf8(address),
                provinceUTF: StringUtils.removeUtf8(province),
                districtUTF: StringUtils.removeUtf8(district),
                content,
                province,
                district,
                ward,
                street,
                location,
                businessTypes,
              };

              if (!betweenTime || betweenTime == 'null') {
                return ChildRouter.responseError(
                  'Bạn chưa chọn thời gian phân biệt nửa ngày',
                  res
                );
              }
              obj.betweenTime = betweenTime;

              if (timeOpen == 'null' || timeClose == 'null') {
                return ChildRouter.responseError('Thời gian không hợp lệ', res);
              }
              obj = { ...obj, timeOpen, timeClose };
              if (
                req.upload &&
                req.upload.pictures &&
                req.upload.pictures.length > 0
              ) {
                obj.pictures = [];
                for (let j = 0; j < req.upload.pictures.length; j++) {
                  obj.pictures.push(req.upload.pictures[j].path);
                }
              } else {
                return ChildRouter.responseError('Cần tải lên đủ ảnh', res);
              }

              if (req.upload && req.upload.thumbnail) {
                obj.thumbnail = req.upload.thumbnail[0].path;
              }
              await ServicesModel.MODEL.addServices(obj);
              return ChildRouter.responseSuccess('Thành công', res);
            },
          ],
        },
      },

      '/delete-service/:serviceId.html': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              let service = await ServicesModel.MODEL.getServicesById(
                req.params.serviceId
              );
              if (service.userId == req.user._id) {
                await ServicesModel.MODEL.updateServices(req.params.serviceId, {
                  status: 2,
                });
                switch (service.type) {
                  case 0:
                    ProducModel.MODEL.updateWhereClause(
                      { storeId: service._id },
                      { status: 2 }
                    );
                    break;
                }
              }
              return ChildRouter.responseSuccess('Thành công', res);
            },
          ],
        },
      },
      '/hide-service/:serviceId.html': {
        config: {
          auth: [this.roles.store],
          get: 'json', // nếu có update cần correct với bên api của admin
        },

        methods: {
          get: [async function (req, res) {
            let service = await ServicesModel.MODEL.getServicesById(req.params.serviceId);
            await ServicesModel.MODEL.updateServices(req.params.serviceId, {status: 0});
            let condition = { storeId: req.params.serviceId }
            // shop
            if (service.businessTypes.includes(TypeServices.SHOP.toString()))
            {
              await ProducModel.MODEL.updateWhereClause(condition, {status: 0}); // show sản phẩm đang ẩn
            }

            // spa
            if (service.businessTypes.includes(TypeServices.SPA.toString()))
            {
              await ServiceOfSpaModel.MODEL.updateWhereClause(condition, {status: 0});
            }


            if (service.businessTypes.includes(TypeServices.HOTEL.toString()))
            {
              await RoomOfHotelModel.MODEL.updateWhereClause(condition, {status: 0});
            }

            // gara
            if (service.businessTypes.includes(TypeServices.CLINIC.toString()))
            {
              await ServiceOfClinicModel.MODEL.updateWhereClause(condition, {status: 0});
            }

            return ChildRouter.responseSuccess('Thành công', res);
          }],
        },
      },
      '/update-status-classification/:id.html': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              let product = await ClassificationOfBrandModel.MODEL.getDataById(
                  req.params.id.trim()
              );
              if (
                  !product ||
                  product.userId != req.user._id
              ) {
                return ChildRouter.responseError('Yêu cầu không hợp lệ', res);
              }

              await ClassificationOfBrandModel.MODEL.updateById(
                  req.params.id.trim(),
                  { status: Number(req.query.status) },
              );
              return ChildRouter.responseSuccess('Thành công', res);
            },
          ],
        },
      },
      '/show-service/:serviceId.html': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              let service = await ServicesModel.MODEL.getServicesById(
                req.params.serviceId
              );
              if (service.userId == req.user._id) {
                await ServicesModel.MODEL.updateServices(req.params.serviceId, {
                  status: 1,
                });
                switch (service.type) {
                  case 0:
                    ProducModel.MODEL.updateWhereClause(
                      { storeId: service._id, status: 2 },
                      { status: 1 }
                    );
                    break;
                }
              }
              return ChildRouter.responseSuccess('Thành công', res);
            },
          ],
        },
      },

      '/cap-nhat-thong-tin-van-chuyen-don-hang.html/:id': {
        config: {
          auth: [this.roles.store],
          post: 'json',
        },

        methods: {
          post: [
            async function (req, res) {
              let { name, value } = req.body;
              value = htmlspecialchars(value || '');
              if (name == 'dvVanChuyen' || name == 'maVanChuyen') {
                let request = await RequestBuyProductModel.MODEL.getRequestById(
                  req.params.id
                );
                if (!request && request.storeId != req.user._id)
                  return ChildRouter.responseError('Yêu cầu không hợp lệ', res);
                await RequestBuyProductModel.MODEL.updateById(req.params.id, {
                  [name]: value,
                });
                return ChildRouter.responseSuccess('Thành công', res);
              } else {
                return ChildRouter.responseError('Yêu cầu không hợp lệ', res);
              }
            },
          ],
        },
      },

      '/gui-anh-tra-hang.html/:id': {
        config: {
          auth: [this.roles.store],
          post: 'json',
          upload: [{ maxCount: 15, name: 'pictures' }],
        },

        methods: {
          post: [
            async function (req, res) {
              let request = await RequestBuyProductModel.MODEL.getRequestById(
                req.params.id
              );
              if (
                (!request && request.storeId != req.user._id) ||
                request.adminConfirm == 1
              )
                return ChildRouter.responseError('Yêu cầu không hợp lệ', res);

              // if (request.imageConfirm && request.imageConfirm.length > 0) {
              //   request.imageConfirm.forEach((img) => {
              //     FileUtils.deleteFile(`.${img.path}`);
              //   });
              // }
              let admin = await UserModel.MODEL.getOneUsersByCondition({
                type: 0,
              });
              if (admin._id)
              {
                UserModel.MODEL.addNotificationBillReturn(admin._id, io);
                await RequestBuyProductModel.MODEL.updateById(req.params.id, {
                  imageConfirm: req.upload.pictures ? req.upload.pictures.map(x=>x.path) : [],
                  timeSendRequest: new Date().getTime(),
                  status: 4,
                });
              } else {
                return ChildRouter.responseError('Xảy ra lỗi', res);
              }

              return ChildRouter.responseSuccess('Thành công', res);
            },
          ],
        },
      },

      '/lich-su-thanh-toan-online.html': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              let { search, page, itemsPerPage, timeStart, timeEnd } =
                req.query;
              if (!search) search = '';
              if (!page) page = 1;
              if (!itemsPerPage) itemsPerPage = 30;

              let where = {};
              if (search != '') {
                where['$or'] = [
                  { userName: new RegExp(search, 'i') },
                  { email: new RegExp(search, 'i') },
                  { phone: new RegExp(search, 'i') },
                  { address: new RegExp(search, 'i') },
                  { fullName: new RegExp(search, 'i') },
                ];
              }
              where['userId'] = req.user._id;

              let histories = await PayingWalletLogModel.MODEL.getLogsData(
                timeEnd != '' && timeStart != ''
                  ? {
                      createAt: {
                        $lte: new Date(timeEnd).getTime() + 60000,
                        $gte: new Date(timeStart).getTime() - 60000,
                      },
                    }
                  : {},
                where,
                Number(page),
                Number(itemsPerPage)
              );

              return ChildRouter.responseSuccess('Thanh cong', res, {
                histories,
              });
            },
          ],
        },
      },

      '/them-co-so.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/co-so/them-co-so.ejs',
          get: 'view',
          title: 'Thêm cơ sở',
          upload: [{ name: 'pictures', maxCount: 15 }],
          post: 'json',
          validate: {
            body: {
              method: ['post'],
              validate: [
                {
                  key: 'name',
                  type: this.dataType.string,
                  name: 'Tên cơ sở',
                  min: 5,
                },
                {
                  key: 'address',
                  type: this.dataType.string,
                  name: 'Địa chỉ',
                  min: 5,
                },
                {
                  key: 'lat',
                  type: this.dataType.string,
                  name: 'Kinh độ theo địa chỉ',
                  min: 5,
                },
                {
                  key: 'lng',
                  type: this.dataType.string,
                  name: 'Vĩ độ theo địa chỉ',
                  min: 5,
                },
              ],
            },
          },
        },

        methods: {
          get: [
            async function (req, res) {
              let brands = await ServicesModel.MODEL.getServicesByCondition({
                userId: req.user._id,
                status: 1,
              });
              return ChildRouter.renderOrResponse(req, res, {
                dateTimes,
                brands,
              });
            },
          ],
          post: [
            async function (req, res) {
              let {
                name,
                address,
                storeId,
                content,
                timeOpen,
                timeClose,
                lat,
                lng,
                phone,
                province,
                district,
                ward,
              } = req.body;

              let obj = {
                userId: req.user._id,
                storeId: storeId,
                name,
                address,
                nameUTF: StringUtils.removeUtf8(name),
                addressUTF: StringUtils.removeUtf8(address),
                content,
                lat,
                lng,
                location: {
                  type: 'Point',
                  coordinates: [
                    parseFloat(lng || '105.804817'),
                    parseFloat(lat || '21.028511'),
                  ],
                },
                phone,
                province,
                district,
                ward
              };

              if (timeOpen === 'null' || timeClose === 'null') {
                return ChildRouter.responseError('Thời gian không hợp lệ', res);
              }
              obj = { ...obj, timeOpen, timeClose };

              const rs = await BranchModel.MODEL.addBranch(obj);
              if (rs)
                return ChildRouter.responseSuccess('Thành công', res, {
                  storeId,
                  rs,
                });
              else
                return ChildRouter.responseError('Xảy ra lỗi', res, {
                  storeId,
                });
            },
          ],
        },
      },
      '/co-so.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/co-so/co-so.ejs',
          get: 'view',
          title: 'Danh sách cơ sở',
        },

        methods: {
          get: [
            async function (req, res) {
              let { storeId } = req.query;
              let danhsachCoSo = await BranchModel.MODEL.getBranchByCondition({
                status: { $ne: 2 },
                storeId: storeId,
              });
              return ChildRouter.renderOrResponse(req, res, { danhsachCoSo });
            },
          ],
        },
      },
      '/chinh-sua-co-so/:branchId.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/co-so/chinh-sua-co-so.ejs',
          get: 'view',
          title: 'Chỉnh sửa cơ sở',
          upload: [{ name: 'pictures', maxCount: 15 }],
          post: 'json',
          validate: {
            body: {
              method: ['post'],
              validate: [
                {
                  key: 'name',
                  type: this.dataType.string,
                  name: 'Tên cơ sở',
                  min: 5,
                },
                {
                  key: 'address',
                  type: this.dataType.string,
                  name: 'Địa chỉ',
                  min: 5,
                },
                {
                  key: 'content',
                  type: this.dataType.string,
                  name: 'Giới thiệu',
                },
                {
                  key: 'storeId',
                  type: this.dataType.string,
                  name: 'Thương hiệu',
                  min: 5,
                },
                {
                  key: 'lat',
                  type: this.dataType.string,
                  name: 'Kinh độ theo địa chỉ',
                  min: 5,
                },
                {
                  key: 'lng',
                  type: this.dataType.string,
                  name: 'Vĩ độ theo địa chỉ',
                  min: 5,
                },
              ],
            },
          },
        },
        methods: {
          get: [
            async function (req, res) {
              let brands = await ServicesModel.MODEL.getServicesByCondition({
                userId: req.user._id,
                status: 1,
              });
              let branch = await BranchModel.MODEL.getBranchById(
                req.params.branchId
              );
              return ChildRouter.renderOrResponse(req, res, {
                dateTimes,
                brands,
                branch,
              });
            },
          ],
          post: [
            async function (req, res) {
              let branch = await BranchModel.MODEL.getBranchById(
                req.params.branchId
              );
              let {
                name,
                address,
                storeId,
                content,
                timeOpen,
                timeClose,
                phone,
                lat,
                lng,
                hotline,
                province,
                district,
                ward
              } = req.body;

              let obj = {
                userId: req.user._id,
                storeId: storeId,
                name,
                address,
                nameUTF: StringUtils.removeUtf8(name),
                addressUTF: StringUtils.removeUtf8(address),
                content,
                phone,
                lat,
                lng,
                location: {
                  type: 'Point',
                  coordinates: [parseFloat(lng), parseFloat(lat)],
                },
                hotline,
                province,
                district,
                ward
              };

              if (timeOpen === 'null' || timeClose === 'null') {
                return ChildRouter.responseError('Thời gian không hợp lệ', res);
              }
              obj = { ...obj, timeOpen, timeClose };

              await BranchModel.MODEL.updateBranch(req.params.branchId, obj);

              return ChildRouter.responseSuccess('Thành công', res, {
                storeId,
              });
            },
          ],
        },
      },
      '/delete-branch/:branchId.html': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },
        methods: {
          get: [
            async function (req, res) {
              await BranchModel.MODEL.deleteBranchByCondition({
                _id: req.params.branchId,
              });
              return ChildRouter.responseSuccess('Thành công', res);
            },
          ],
        },
      },

      '/quan-ly-coupon.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/quan-ly-coupon.ejs',
          get: 'view',
          title: 'Quản lý coupon',
        },

        methods: {
          get: [
            async function (req, res) {
              return ChildRouter.renderOrResponse(req, res, {});
            },
          ],
        },
      },

      '/tao-coupon.html': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/tao-coupon.ejs',
          get: 'view',
          title: 'Tạo coupon',
          post: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              let stores = await ServicesModel.MODEL.getServicesByCondition({
                userId: req.user._id,
                status: 1,
              });
              return ChildRouter.renderOrResponse(req, res, { stores });
            },
          ],

          post: [
            async function (req, res) {
              let userId = req.user._id;
              let {
                startTime,
                endTime,
                countBooking,
                countCoupon,
                type,
                value,
                minBillValue,
                storeId,
              } = req.body;
              let typeCode = 1;
              if (!startTime) startTime = -1;
              if (!endTime) endTime = -1;
              if (!countBooking) countBooking = -1;
              if (!countCoupon) countCoupon = 1;
              type = Number(type);
              value = Number(value);
              minBillValue = Number(minBillValue);
              storeId = String(storeId);

              let rs = await CouponModel.MODEL.createCoupon(
                Number(startTime),
                Number(endTime),
                Number(countBooking),
                Number(countCoupon),
                type,
                value,
                minBillValue,
                storeId,
                typeCode,
                userId
              );
              return ChildRouter.responseSuccess('Thành công', res);
            },
          ],
        },
      },

      '/sua-coupon.html/:id': {
        config: {
          auth: [this.roles.store],
          view: 'public/index.ejs',
          inc: 'inc/store/tao-coupon.ejs',
          get: 'view',
          title: 'Sửa coupon',
          post: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              let stores = await ServicesModel.MODEL.getServicesByCondition({
                userId: req.user._id,
                status: 1,
              });
              let coupon = await CouponModel.MODEL.getDataById(req.params.id);
              return ChildRouter.renderOrResponse(req, res, { coupon, stores });
            },
          ],

          post: [
            async function (req, res) {
              let coupon = await CouponModel.MODEL.getDataById(req.params.id);
              let {
                startTime,
                endTime,
                countBooking,
                type,
                value,
                minBillValue,
              } = req.body;
              if (!startTime) startTime = -1;
              if (!endTime) endTime = -1;
              if (!countBooking) countBooking = -1;
              let status = 1;
              if (coupon.currentBooking >= Number(countBooking)) {
                status = 0;
              } else {
                status = 1;

                if (new Date().getTime() > coupon.endTime) {
                  status = 0;
                }
              }

              await CouponModel.MODEL.updateById(req.params.id, {
                startTime: Number(startTime),
                endTime: Number(endTime),
                countBooking: Number(countBooking),
                type: Number(type),
                value: Number(value),
                minBillValue: Number(minBillValue),
                status,
              });
              return ChildRouter.responseSuccess('Thành công', res);
            },
          ],
        },
      },

      '/get-coupon-status.html/:status': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              let status = Number(req.params.status);
              let userId = req.user._id;
              let coupons = await CouponModel.MODEL.getCouponByCondition({
                status: status,
                userId,
              });
              return ChildRouter.responseSuccess('Thanh cong', res, {
                coupons,
              });
            },
          ],
        },
      },

      '/change-coupon-status.html/:id/:status': {
        config: {
          auth: [this.roles.store],
          get: 'json',
        },

        methods: {
          get: [
            async function (req, res) {
              await CouponModel.MODEL.updateById(req.params.id, {
                status: req.params.status,
              });
              return ChildRouter.responseSuccess('Thành công', res);
            },
          ],
        },
      },
    };
  }
};
