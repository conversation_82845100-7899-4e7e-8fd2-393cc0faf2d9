import { Component, ViewEncapsulation, HostBinding, Input } from '@angular/core';

import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
@Component({
  selector: 'app-footer-content',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  imports: [CommonModule, RouterModule],
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss'],
})
export class FooterContentComponent {
  @HostBinding('style.display') display = 'contents';

  constructor() {}

  /** Value props */
  @Input() image616: string = '';
  /** Style props */
  @Input() frameFooterPadding: string | number = '';
  @Input() rectangleDivHeight: string | number = '';

  get frameFooterStyle() {
    return {
      padding: this.frameFooterPadding,
    };
  }

  get rectangleDiv3Style() {
    return {
      height: this.rectangleDivHeight,
    };
  }
}
