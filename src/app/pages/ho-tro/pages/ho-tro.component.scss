.support-page {  
  margin: 0 auto;
  padding: 20px;
  margin-top: 40px;
  .back-button {
    margin-bottom: 20px;

    .btn {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      border-radius: 6px;
      text-decoration: none;
      transition: all 0.3s ease;

      &:hover {
        transform: translateX(-2px);
      }
    }
  }

  .page-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;

    .page-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 10px;
      line-height: 1.2;
    }

    .page-meta {
      display: flex;
      align-items: center;
      gap: 20px;
      color: #6c757d;
      font-size: 0.95rem;

      .updated-date {
        display: flex;
        align-items: center;
        gap: 5px;
      }
    }
  }

  .page-content {
    margin-bottom: 40px;

    .content-body {
      font-size: 1.1rem;
      line-height: 1.8;
      color: #495057;

      // Style for content from CKEditor
      :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
        margin-top: 30px;
        margin-bottom: 15px;
        font-weight: 600;
        color: #2c3e50;
      }

      :deep(h1) { font-size: 2.2rem; }
      :deep(h2) { font-size: 1.8rem; }
      :deep(h3) { font-size: 1.5rem; }
      :deep(h4) { font-size: 1.3rem; }
      :deep(h5) { font-size: 1.1rem; }
      :deep(h6) { font-size: 1rem; }

      :deep(p) {
        margin-bottom: 16px;
        text-align: justify;
      }

      :deep(ul), :deep(ol) {
        margin-bottom: 16px;
        padding-left: 30px;

        li {
          margin-bottom: 8px;
        }
      }

      :deep(blockquote) {
        border-left: 4px solid #007bff;
        padding-left: 20px;
        margin: 20px 0;
        font-style: italic;
        background-color: #f8f9fa;
        padding: 15px 20px;
        border-radius: 4px;
      }

      :deep(img) {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
        margin: 20px 0;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);

        &.img-error {
          opacity: 0.5;
          filter: grayscale(100%);
        }
      }

      :deep(table) {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
        background-color: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);

        th, td {
          padding: 12px 15px;
          text-align: left;
          border-bottom: 1px solid #e9ecef;
        }

        th {
          background-color: #f8f9fa;
          font-weight: 600;
          color: #495057;
        }

        tr:hover {
          background-color: #f8f9fa;
        }
      }

      :deep(a) {
        color: #007bff;
        text-decoration: none;
        border-bottom: 1px solid transparent;
        transition: all 0.3s ease;

        &:hover {
          border-bottom-color: #007bff;
        }
      }

      :deep(code) {
        background-color: #f8f9fa;
        padding: 2px 6px;
        border-radius: 4px;
        font-family: 'Courier New', monospace;
        font-size: 0.9em;
        color: #e83e8c;
      }

      :deep(pre) {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 6px;
        overflow-x: auto;
        margin: 20px 0;

        code {
          background: none;
          padding: 0;
          color: #495057;
        }
      }
    }

    .no-content {
      text-align: center;
      padding: 40px 20px;

      .alert {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        max-width: 500px;
        margin: 0 auto;
      }
    }
  }

  .related-pages {
    background-color: #f8f9fa;
    padding: 30px;
    border-radius: 12px;
    border: 1px solid #e9ecef;

    .related-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 20px;
      text-align: center;
    }

    .related-links {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;

      .related-link {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        background-color: white;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        text-decoration: none;
        color: #495057;
        transition: all 0.3s ease;
        font-weight: 500;

        &:hover {
          border-color: #007bff;
          color: #007bff;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0,123,255,0.15);
        }

        &.active {
          border-color: #007bff;
          background-color: #e7f3ff;
          color: #007bff;
        }

        i {
          font-size: 1.2rem;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .support-page {
    padding: 15px;

    .page-header {
      .page-title {
        font-size: 2rem;
      }

      .page-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
      }
    }

    .page-content {
      .content-body {
        font-size: 1rem;

        :deep(h1) { font-size: 1.8rem; }
        :deep(h2) { font-size: 1.5rem; }
        :deep(h3) { font-size: 1.3rem; }
      }
    }

    .related-pages {
      padding: 20px;

      .related-links {
        grid-template-columns: 1fr;
      }
    }
  }
}

@media (max-width: 480px) {
  .support-page {
    padding: 10px;

    .page-header {
      .page-title {
        font-size: 1.5rem;
      }
    }

    .related-pages {
      padding: 15px;

      .related-title {
        font-size: 1.3rem;
      }

      .related-links {
        .related-link {
          padding: 12px 15px;
          font-size: 0.9rem;
        }
      }
    }
  }
}