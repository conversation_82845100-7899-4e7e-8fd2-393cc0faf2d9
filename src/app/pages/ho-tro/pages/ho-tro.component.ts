import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { HoTroService } from 'src/app/core/services/ho-tro.service';

@Component({
  selector: 'app-ho-tro',
  templateUrl: './ho-tro.component.html',
  styleUrls: ['./ho-tro.component.scss'],
  standalone: true,
  imports: [CommonModule, RouterModule],
})
export class HoTroComponent implements OnInit {
  pageName: string = '';
  pageContent: string = '';
  pageTitle: string = '';
  loading = false;
  safeContent: SafeHtml | null = null;

  // Mapping page names to titles
  private pageTitles: { [key: string]: string } = {
    'trung-tam-ho-tro': 'Trung tâm hỗ trợ',
    'chinh-sach-bao-mat': '<PERSON><PERSON>h sách bảo mật',
    'dieu-khoan-dich-vu': 'Điều khoản dịch vụ',
    'gioi-thieu': 'Giới thiệu',
  };

  // Mapping page names to API field names
  private pageFields: { [key: string]: string } = {
    'trung-tam-ho-tro': 'trungTamHoTro',
    'chinh-sach-bao-mat': 'chinhSachBaoMat',
    'dieu-khoan-dich-vu': 'dieuKhoanDichVu',
    'gioi-thieu': 'gioiThieu',
  };

  constructor(
    private route: ActivatedRoute,
    private hoTroService: HoTroService,
    private sanitizer: DomSanitizer,
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      this.pageName = params.get('pageName') || '';
      if (this.pageName && this.pageTitles[this.pageName]) {
        this.pageTitle = this.pageTitles[this.pageName];
        this.getPageContent();
      }
    });
  }

  getPageContent() {
    this.loading = true;
    this.hoTroService.getHoTroData().subscribe({
      next: (response: any) => {
        if (!response.error && response.data) {
          const fieldName = this.pageFields[this.pageName];
          if (fieldName && response.data[fieldName]) {
            this.pageContent = response.data[fieldName];
            // Sanitize the HTML content to prevent XSS
            this.safeContent = this.sanitizer.bypassSecurityTrustHtml(this.pageContent);
          } else {
            this.pageContent = '';
            this.safeContent = null;
          }
        }
        this.loading = false;
      },
      error: error => {
        console.error('Error fetching support page content:', error);
        this.loading = false;
      },
    });
  }

  /**
   * Xử lý trường hợp ảnh bị lỗi
   * @param event Event từ DOM
   */
  handleImageError(event: Event): void {
    const imgElement = event.target as HTMLImageElement;
    // Loại bỏ ảnh nguồn bị lỗi
    imgElement.src = 'assets/images/placeholder.jpg';
    // Thêm class để styling riêng nếu cần
    imgElement.classList.add('img-error');
  }
}
