<div class="container">
  <div class="support-page">
    <!-- Loading indicator -->
    <div *ngIf="loading" class="text-center my-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden"><PERSON><PERSON> tải...</span>
      </div>
    </div>

    <!-- Support Page Content -->
    <ng-container *ngIf="!loading && pageTitle">
      <!-- <div class="back-button mb-3">
        <a routerLink="/home" class="btn btn-outline-secondary">
          <i class="bi bi-arrow-left"></i> Quay lại trang chủ
        </a>
      </div> -->

      <div class="page-header">
        <h1 class="page-title">{{ pageTitle }}</h1>
        <div class="page-meta">
          <!-- <span class="updated-date">
            <i class="bi bi-calendar me-1"></i>
            Cập nhật: {{ currentDate | date: 'dd/MM/yyyy' }}
          </span> -->
        </div>
      </div>

      <div class="page-content">
        <div *ngIf="safeContent" [innerHTML]="safeContent" class="content-body"></div>
        <div *ngIf="!safeContent && !loading" class="no-content">
          <div class="alert alert-info">
            <i class="bi bi-info-circle me-2"></i>
            Nội dung đang được cập nhật. Vui lòng quay lại sau.
          </div>
        </div>
      </div>
      
      <!-- <div class="related-pages mt-5">
        <h3 class="related-title">Các trang khác</h3>
        <div class="related-links">
          <a [routerLink]="['/ho-tro', 'trung-tam-ho-tro']" 
             [class.active]="pageName === 'trung-tam-ho-tro'"
             class="related-link">
            <i class="bi bi-headset me-2"></i>
            Trung tâm hỗ trợ
          </a>
          <a [routerLink]="['/ho-tro', 'chinh-sach-bao-mat']" 
             [class.active]="pageName === 'chinh-sach-bao-mat'"
             class="related-link">
            <i class="bi bi-shield-check me-2"></i>
            Chính sách bảo mật
          </a>
          <a [routerLink]="['/ho-tro', 'dieu-khoan-dich-vu']" 
             [class.active]="pageName === 'dieu-khoan-dich-vu'"
             class="related-link">
            <i class="bi bi-file-text me-2"></i>
            Điều khoản dịch vụ
          </a>
          <a [routerLink]="['/ho-tro', 'gioi-thieu']" 
             [class.active]="pageName === 'gioi-thieu'"
             class="related-link">
            <i class="bi bi-info-circle me-2"></i>
            Giới thiệu
          </a>
        </div>
      </div> -->
    </ng-container>

    <!-- Error state -->
    <div *ngIf="!loading && !pageTitle" class="alert alert-warning my-5">
      Không tìm thấy trang hỗ trợ này.
      <div class="mt-3">
        <a routerLink="/home" class="btn btn-primary">Quay lại trang chủ</a>
      </div>
    </div>
  </div>
</div>