import { Component, ViewEncapsulation, HostBinding, On<PERSON>nit, OnDestroy } from '@angular/core';

import { ProductItem } from '../../components/product-item/product-item.component';

import { NgForOf, NgIf } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';
import { ApiService } from '../../core/services/api.service';
import { BannerService, Banner } from '../../core/services/banner.service';
import { Subject } from 'rxjs';
import {finalize, takeUntil} from 'rxjs/operators';
import { ProductDetailDialog } from '../../components/product-detail-dialog/product-detail-dialog.component';
import { environment } from '../../../environments/environment';
import {LoadingService} from "../../components/loading/loading.service";

@Component({
  selector: 'cho-hai-san',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  imports: [ProductItem, NgForOf, NgI<PERSON>],
  templateUrl: './ChoHaiSan.component.html',
  styleUrls: ['./ChoHaiSan.component.scss'],
})
export class <PERSON><PERSON>aiSan implements OnInit, OnDestroy {
  @HostBinding('style.display') display = 'contents';

  private destroy$ = new Subject<void>();

  // API data
  categories: any[] = [];
  products: any[] = [];
  loading = false;
  hasMore = true;

  // Pagination
  currentPage = 1;
  limit = 10;
  selectedCategoryId = '';

  // Banner data
  banners: Banner[] = [];
  isLoadingBanners = false;
  bannerError: string | null = null;
  private autoSlideInterval: any;
  currentSlideIndex: number = 0;

  // Mock categories for fallback
  mockCategories = ['THỰC PHẨM TƯƠI MỖI NGÀY', 'THỰC PHẨM SƠ CHẾ', 'THỰC PHẨM CHẾ BIẾN SẴN'];

  constructor(
    private dialog: MatDialog,
    private apiService: ApiService,
    private bannerService: BannerService,
    private loadingService: LoadingService,
  ) {}

  ngOnInit() {
    this.loadBanners();
    this.loadCategories();
    this.loadProducts();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    // Clear auto-slide interval
    if (this.autoSlideInterval) {
      clearInterval(this.autoSlideInterval);
    }
  }

  /**
   * Load banners for cho hai san
   */
  loadBanners() {
    this.isLoadingBanners = true;
    this.bannerError = null;

    this.bannerService
      .getBannerStores('cho_hai_san')
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: response => {
          this.isLoadingBanners = false;
          if (!response.error && response.data?.banners) {
            this.banners = response.data.banners;
            // Start auto-slide if there are multiple banners
            if (this.banners.length > 1) {
              this.startAutoSlide();
            }
          } else {
            this.bannerError = response.message || 'Không thể tải banner';
          }
        },
        error: error => {
          this.isLoadingBanners = false;
          this.bannerError = 'Lỗi kết nối. Vui lòng thử lại.';
          console.error('Error loading banners:', error);
        },
      });
  }

  /**
   * Load categories from API - sử dụng API categories-by-type giống như menu component
   */
  loadCategories() {
    // Sử dụng API categories-by-type với cat=0,1,2 cho sản phẩm
    this.apiService
      .get('user/api/categories-by-type', { cat: '0,1,2' })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          console.log('Categories response:', response);

          if (response && !response.error && response.data?.categories) {
            this.categories = response.data.categories;
          } else {
            // Fallback to mock categories
            this.categories = this.mockCategories.map((name, index) => ({
              _id: `mock_${index}`,
              name: name,
            }));
          }
        },
        error: error => {
          console.error('Error loading categories:', error);
          // Fallback to mock categories
          this.categories = this.mockCategories.map((name, index) => ({
            _id: `mock_${index}`,
            name: name,
          }));
        },
      });
  }

  /**
   * Load products from API
   */
  loadProducts() {
    this.loading = true;
    this.loadingService.show();
    const params: any = {
      search: '',
      type: 0, // 0 = sản phẩm
      page: this.currentPage,
      limit: this.limit,
    };

    if (this.selectedCategoryId) {
      params.categoryId = this.selectedCategoryId;
    }

    this.apiService
      .get('/user/api/search-v2', params)
      .pipe(takeUntil(this.destroy$))
      .pipe(finalize(() => {
          this.loadingService.hide();
        }))
      .subscribe({
        next: (response: any) => {
          this.loading = false;

          if (response && !response.error && response.data?.result) {
            const newProducts = response.data.result.map((product: any) => ({
              _id: product._id,
              name: product.name,
              price: this.formatPrice(product.price),
              priceDiscount: product.priceOld ? this.formatPrice(product.priceOld) : null,
              discount: product.priceOld
                ? this.calculateDiscount(product.price, product.priceOld)
                : null,
              image: this.getProductImageUrl(product.thumbail),
              originalData: product,
            }));

            if (this.currentPage === 1) {
              this.products = newProducts;
            } else {
              this.products = [...this.products, ...newProducts];
            }

            this.hasMore = this.currentPage < (response.data.totalPage || 1);
          }
        },
        error: error => {
          this.loading = false;
          console.error('Error loading products:', error);
        },
      });
  }

  /**
   * Load more products
   */
  onLoadMore() {
    if (!this.loading && this.hasMore) {
      this.currentPage++;
      this.loadProducts();
    }
  }

  /**
   * Filter products by category
   */
  onCategorySelect(categoryId: string) {
    this.selectedCategoryId = categoryId;
    this.currentPage = 1;
    this.products = [];
    this.loadProducts();
  }

  /**
   * Format price to Vietnamese currency
   */
  formatPrice(price: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price);
  }

  /**
   * Calculate discount percentage
   */
  calculateDiscount(currentPrice: number, oldPrice: number): string {
    if (!oldPrice || oldPrice <= currentPrice) return '0';
    const discount = Math.round(((oldPrice - currentPrice) / oldPrice) * 100);
    return discount.toString();
  }

  /**
   * Get product image URL
   */
  getProductImageUrl(thumbail: string): string {
    if (!thumbail) return 'assets/<EMAIL>';
    if (thumbail.startsWith('http')) return thumbail;
    return `${environment.imageApiUrl}/${thumbail}`;
  }

  /**
   * Track by function for ngFor performance
   */
  trackByProductId(index: number, product: any): string {
    return product._id || index.toString();
  }

  /**
   * Open product detail dialog
   */
  onProductClick(product: any) {
    const dialogRef = this.dialog.open(ProductDetailDialog, {
      width: '90vw',
      maxWidth: '1200px',
      maxHeight: '90vh',
      disableClose: false,
      autoFocus: false,
      data: {
        productId: product._id,
        productImage: product.image,
      },
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        if (result.action === 'addToCart') {
          console.log('Add to cart:', result.data);
          // Handle add to cart logic
        } else if (result.action === 'buyNow') {
          console.log('Buy now:', result.data);
          // Handle buy now logic
        }
      }
    });
  }

  /**
   * Get banner image URL
   */
  getBannerImageUrl(banner: Banner): string {
    if (!banner.thumbail) return 'assets/<EMAIL>';
    if (banner.thumbail.startsWith('http')) return banner.thumbail;
    return `${environment.imageApiUrl}/${encodeURIComponent(banner.thumbail)}`;
  }

  /**
   * Handle banner click
   */
  onImageClick(banner?: Banner) {
    if (banner && banner.params) {
      // Navigate to banner link if available
      window.open(banner.params, '_blank');
    } else {
      // Please sync "Tin tuc chi tiet" to the project
      console.log('Banner clicked:', banner);
    }
  }

  onOrderClick() {}

  startAutoSlide(): void {
    // Clear any existing interval
    if (this.autoSlideInterval) {
      clearInterval(this.autoSlideInterval);
    }

    // Start auto-slide every 3 seconds
    this.autoSlideInterval = setInterval(() => {
      this.nextSlide();
    }, 3000);
  }

  nextSlide(): void {
    if (this.banners.length > 0) {
      this.currentSlideIndex = (this.currentSlideIndex + 1) % this.banners.length;

      // Trigger Bootstrap carousel to move to next slide
      const carousel = document.getElementById('choHaiSanCarousel');
      if (carousel) {
        const nextButton = carousel.querySelector('.carousel-control-next') as HTMLElement;
        if (nextButton) {
          nextButton.click();
        }
      }
    }
  }

  pauseAutoSlide(): void {
    if (this.autoSlideInterval) {
      clearInterval(this.autoSlideInterval);
    }
  }

  resumeAutoSlide(): void {
    if (this.banners.length > 1) {
      this.startAutoSlide();
    }
  }
}
