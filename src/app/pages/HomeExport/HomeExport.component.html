<!-- Bootstrap Carousel Slider - Full Width -->
<div id="homeCarousel" class="carousel slide full-width-carousel" data-bs-ride="carousel" 
     (mouseenter)="pauseAutoSlide()" (mouseleave)="resumeAutoSlide()">
  <!-- Loading handled by global loading service -->

  <!-- Error State -->
  <div *ngIf="!isLoading && error" class="alert alert-danger m-3">
    {{ error }}
  </div>

  <!-- Carousel Content when data loaded -->
  <ng-container *ngIf="!isLoading && !error && banners.length > 0">
    <!-- Carousel indicators -->
    <div class="carousel-indicators">
      <button
        *ngFor="let banner of banners; let i = index"
        type="button"
        data-bs-target="#homeCarousel"
        [attr.data-bs-slide-to]="i"
        [class.active]="i === 0"
        [attr.aria-current]="i === 0 ? 'true' : null"
        [attr.aria-label]="'Slide ' + (i + 1)"
      ></button>
    </div>

    <!-- Carousel items -->
    <div class="carousel-inner">
      <div
        *ngFor="let banner of banners; let i = index"
        class="carousel-item"
        [class.active]="i === 0"
        (click)="onImageClick(banner)"
      >
        <img
          [src]="getBannerImageUrl(banner)"
          class="d-block w-100 carousel-image"
          [alt]="banner.name"
          style="cursor: pointer"
        />
        <div class="carousel-caption d-none d-md-block">
          <!-- <h5>{{ banner.name }}</h5> -->
        </div>
      </div>
    </div>

    <!-- No banners state -->
    <div *ngIf="banners.length === 0" class="text-center p-5">
      <p>Không có banner nào.</p>
    </div>

    <!-- Navigation controls -->
    <button
      class="carousel-control-prev"
      type="button"
      data-bs-target="#homeCarousel"
      data-bs-slide="prev"
    >
      <span class="carousel-control-prev-icon" aria-hidden="true"></span>
      <span class="visually-hidden">Previous</span>
    </button>
    <button
      class="carousel-control-next"
      type="button"
      data-bs-target="#homeCarousel"
      data-bs-slide="next"
    >
      <span class="carousel-control-next-icon" aria-hidden="true"></span>
      <span class="visually-hidden">Next</span>
    </button>
  </ng-container>
</div>

<div class="home-export">
  <div class="container py-5">
    <!-- Loading State -->
    <div *ngIf="isCategoryHomeLoading" class="text-center p-5">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">Đang tải...</span>
      </div>
    </div>

    <!-- Error State -->
    <div *ngIf="!isCategoryHomeLoading && categoryHomeError" class="alert alert-danger">
      {{ categoryHomeError }}
    </div>

    <!-- Category Home Banners -->
    <div *ngIf="!isCategoryHomeLoading && !categoryHomeError" class="row g-4">
      <div
        *ngFor="let banner of categoryHomeBanners; let i = index"
        class="col-md-6"
        [class.col-md-12]="categoryHomeBanners.length === 1"
        [class.col-md-4]="categoryHomeBanners.length === 3"
        [class.col-md-3]="categoryHomeBanners.length === 4"
      >
        <div class="seafood-content" (click)="onImageClick(banner)" style="cursor: pointer">
          <img
            [src]="getBannerImageUrl(banner)"
            [alt]="banner.name"
            class="seafood-image"
          />
          <div class="content-overlay"></div>
          <h3 class="seafood-title">{{ banner.name }}</h3>
        </div>
      </div>
    </div>
  </div>
</div>
