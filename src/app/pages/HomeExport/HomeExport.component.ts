import { Component, ViewEncapsulation, HostBinding, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BannerService, Banner } from '../../core/services/banner.service';
import { Subscription } from 'rxjs';
import { LoadingService } from '../../components/loading/loading.service';
import { environment } from 'src/environments/environment';
import {Router} from "@angular/router";

@Component({
  selector: 'home-export',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  imports: [CommonModule],
  templateUrl: './HomeExport.component.html',
  styleUrls: ['./HomeExport.component.scss'],
})
export class HomeExport implements OnInit, OnDestroy {
  @HostBinding('style.display') display = 'contents';

  banners: Banner[] = [];
  categoryHomeBanners: Banner[] = [];
  isLoading: boolean = true;
  isCategoryHomeLoading: boolean = true;
  error: string | null = null;
  categoryHomeError: string | null = null;
  private subscription: Subscription = new Subscription();
  private autoSlideInterval: any;
  currentSlideIndex: number = 0;

  constructor(private bannerService: BannerService, private loadingService: LoadingService, private router: Router) {

  }

  ngOnInit(): void {
    this.loadBanners();
    this.loadCategoryHomeBanners();
  }

  ngOnDestroy(): void {
    // Clean up subscriptions to prevent memory leaks
    this.subscription.unsubscribe();
    // Clear auto-slide interval
    if (this.autoSlideInterval) {
      clearInterval(this.autoSlideInterval);
    }
  }

  loadBanners(): void {
    this.isLoading = true;
    this.error = null;

    // Show global loading indicator
    this.loadingService.show();

    const bannerSub = this.bannerService.getBanners().subscribe({
      next: response => {
        this.isLoading = false;
        // Hide global loading indicator
        this.loadingService.hide();

        if (!response.error && response.data.banners) {
          this.banners = response.data.banners;
          console.log('Banners loaded:', this.banners);
          // Start auto-slide if there are multiple banners
          if (this.banners.length > 1) {
            this.startAutoSlide();
          }
        } else {
          this.error = response.message || 'Không thể tải banner';
          console.error('Error in banner response:', response);
        }
      },
      error: err => {
        this.isLoading = false;
        // Hide global loading indicator on error
        this.loadingService.hide();

        this.error = 'Đã xảy ra lỗi khi tải banner';
        console.error('Banner loading error:', err);
      },
    });

    this.subscription.add(bannerSub);
  }

  loadCategoryHomeBanners(): void {
    this.isCategoryHomeLoading = true;
    this.categoryHomeError = null;

    const categoryHomeSub = this.bannerService.getBannerStores('category_home').subscribe({
      next: response => {
        this.isCategoryHomeLoading = false;

        if (!response.error && response.data.banners) {
          this.categoryHomeBanners = response.data.banners;
          console.log('Category home banners loaded:', this.categoryHomeBanners);
        } else {
          this.categoryHomeError = response.message || 'Không thể tải banner category home';
          console.error('Error in category home banner response:', response);
        }
      },
      error: err => {
        this.isCategoryHomeLoading = false;
        this.categoryHomeError = 'Đã xảy ra lỗi khi tải banner category home';
        console.error('Category home banner loading error:', err);
      },
    });

    this.subscription.add(categoryHomeSub);
  }

  getBannerImageUrl(banner: Banner): string {
    // Use Google Cloud Storage URL as the base for banner images
    return banner.thumbail.startsWith('http')
      ? encodeURIComponent(banner.thumbail)
      : `${environment.imageApiUrl}/${encodeURIComponent(banner.thumbail)}`;
  }

  onImageClick(banner: Banner) {
    // Handle banner click based on screen type
    console.log('Banner clicked:', banner);

    // Basic navigation logic based on banner screen type
    if (banner.screen === 'LINK' && banner.params && banner.params.startsWith('http')) {
      window.open(banner.params, '_blank');
    } else if (banner.screen === 'LINK' && banner.params && !banner.params.startsWith('http')) {
      this.router.navigate([banner.params]);
    }

    // Additional navigation logic can be added here
  }

  startAutoSlide(): void {
    // Clear any existing interval
    if (this.autoSlideInterval) {
      clearInterval(this.autoSlideInterval);
    }

    // Start auto-slide every 3 seconds
    this.autoSlideInterval = setInterval(() => {
      this.nextSlide();
    }, 3000);
  }

  nextSlide(): void {
    if (this.banners.length > 0) {
      this.currentSlideIndex = (this.currentSlideIndex + 1) % this.banners.length;
      
      // Trigger Bootstrap carousel to move to next slide
      const carousel = document.getElementById('homeCarousel');
      if (carousel) {
        const nextButton = carousel.querySelector('.carousel-control-next') as HTMLElement;
        if (nextButton) {
          nextButton.click();
        }
      }
    }
  }

  pauseAutoSlide(): void {
    if (this.autoSlideInterval) {
      clearInterval(this.autoSlideInterval);
    }
  }

  resumeAutoSlide(): void {
    if (this.banners.length > 1) {
      this.startAutoSlide();
    }
  }
}
