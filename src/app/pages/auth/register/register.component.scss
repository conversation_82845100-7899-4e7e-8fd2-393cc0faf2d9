// <PERSON><PERSON><PERSON>n màu sắc và kích thước
$primary-color: #005B94;
$light-bg: #f5f5f5;
$card-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
$border-radius: 8px;
$spacing: 16px;

.register-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: $light-bg;
  padding: $spacing;
  background-image: linear-gradient(to bottom right, rgba(44, 111, 172, 0.1), rgba(44, 111, 172, 0.05));
}

.register-card {
  width: 100%;
  max-width: 600px;
  background-color: white;
  border-radius: $border-radius;
  padding: $spacing * 2;
  box-shadow: $card-shadow;
  animation: fadeIn 0.5s ease-in-out;
}

.register-header {
  text-align: center;
  margin-bottom: $spacing * 2;
  
  h2 {
    font-size: 28px;
    color: $primary-color;
    margin-bottom: $spacing / 2;
    font-weight: 500;
  }
  
  p {
    color: #666;
    margin: 0;
  }
}

form {
  display: flex;
  flex-direction: column;
}

.full-width {
  width: 100%;
}

.form-row {
  display: flex;
  gap: $spacing;
  
  mat-form-field {
    flex: 1;
  }
}

.terms-check {
  margin-top: $spacing / 2;
  
  a {
    color: $primary-color;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
  
  mat-error {
    font-size: 12px;
    margin-top: 5px;
    display: block;
  }
}

.register-button {
  padding: 12px !important;
  font-size: 16px;
  margin-top: $spacing;
  background-color: $primary-color !important;
  transition: background-color 0.3s;
  height: 40px;
}

.login-link {
  text-align: center;
  margin-top: $spacing * 1.5;
  
  span {
    color: #666;
  }
  
  a {
    color: $primary-color;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

// Hiệu ứng
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive
@media (max-width: 600px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }
  
  .register-card {
    padding: $spacing;
  }
}