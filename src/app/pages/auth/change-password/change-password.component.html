<div class="change-password-container">
  <div class="change-password-card">
    <h2 class="change-password-title"><PERSON><PERSON><PERSON> mật khẩu</h2>
    <p class="change-password-subtitle"><PERSON><PERSON> lòng nhập mật khẩu hiện tại và mật khẩu mới của bạn.</p>

    <form [formGroup]="changePasswordForm" (ngSubmit)="onSubmit()">
      <app-text-input
        formControlName="currentPassword"
        label="Mật khẩu hiện tại"
        type="password"
        placeholder="Nhập mật khẩu hiện tại"
      ></app-text-input>

      <app-text-input
        formControlName="newPassword"
        label="Mật khẩu mới"
        type="password"
        placeholder="Nhập mật khẩu mới"
      ></app-text-input>

      <app-text-input
        formControlName="confirmPassword"
        label="<PERSON><PERSON><PERSON> nhận mật khẩu mới"
        type="password"
        placeholder="Nhập lại mật khẩu mới"
        [errorMessages]="{
          required: 'Xác nhận mật khẩu là bắt buộc',
          passwordMismatch: 'Mật khẩu không khớp'
        }"
      ></app-text-input>

      <div *ngIf="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>

      <div *ngIf="successMessage" class="success-message">
        {{ successMessage }}
      </div>

      <div class="actions-container">
        <button
          mat-raised-button
          color="primary"
          type="submit"
          [disabled]="changePasswordForm.invalid || isSubmitting"
          class="submit-button"
        >
          <span *ngIf="!isSubmitting">Đổi mật khẩu</span>
          <span *ngIf="isSubmitting">Đang xử lý...</span>
        </button>

        <button
          mat-button
          type="button"
          (click)="onCancel()"
          class="cancel-button"
          [disabled]="isSubmitting"
        >
          Hủy
        </button>
      </div>

      <div class="alternative-actions">
        <a routerLink="/dang-nhap">Đăng nhập</a> |
        <a routerLink="/quen-mat-khau">Quên mật khẩu?</a>
      </div>
    </form>
  </div>
</div>