import {Component, EventEmitter, Output} from '@angular/core';
import {CarouselModule, OwlOptions} from "ngx-owl-carousel-o";
import {
  ExpansionContainerComponent
} from "../../../../layouts/layout-container/components/expansion-container/expansion-container.component";
import {MatButton, MatIconButton} from "@angular/material/button";
import {MatIcon} from "@angular/material/icon";
import {NgClass, NgForOf, NgIf} from "@angular/common";
import {
  BodyStyle,
  Brand,
  Category,
  ColorVehicle,
  FilterConfig,
  Fuel,
  Province,
  VehicleParamsModel
} from "../../../../layouts/layout-container/model";
import {LayoutBusinessLogicService} from "../../../../layouts/layout-container/service/layout-business.service";
import {MatTooltip} from "@angular/material/tooltip";
import moment from "moment";
import {PostNewsBusinessLogicService} from "../../service/post-news-business.service";
import {GlobalConstants} from "../../../../constants";

@Component({
  selector: 'app-first-step',
  standalone: true,
  imports: [
    CarouselModule,
    ExpansionContainerComponent,
    MatButton,
    MatIcon,
    MatIconButton,
    NgForOf,
    NgIf,
    NgClass,
    MatTooltip,
  ],
  templateUrl: './first-step.component.html',
  styleUrl: './first-step.component.scss'
})
export class FirstStepComponent {
  @Output() onNextStep = new EventEmitter<VehicleParamsModel>()
  configFilter?: FilterConfig
  categorySelected?: Category
  listSeat = [2, 4, 5, 7, 9, 12, 16, 29, 45]
  seatSelected?: number
  province: Province[] = []
  provinceSelected?: Province

  currParams?: VehicleParamsModel

  years: number[] = [];

  brandSelected?: Brand

  customOptions: OwlOptions = {
    margin: 10,
    autoWidth: true,
    nav: false,
    loop: true,
    rewind: true,
    dots: false,
    autoplay: true,
    autoplayTimeout: 3000,
    autoplayHoverPause: true,
    responsive: {
      0: {
        items: 3
      },
      400: {
        items: 5
      },
      740: {
        items: 7
      },
      940: {
        items: 7
      }
    },
  };

  constructor(private _businessService: LayoutBusinessLogicService, private _service: PostNewsBusinessLogicService) {
    this._businessService.manufactureSelected$.subscribe(e => {
      this.brandSelected = e
    })
    this._businessService.province$.subscribe(e => {
      this.province = e ?? []
    })
    this._businessService.provinceSelected$.subscribe(e => {
      this.provinceSelected = e
    })
    this._businessService.filterConfig$.subscribe(e => {
      this.configFilter = e
      this.categorySelected = e?.category && e?.category.length > 0 ? e?.category[0] : undefined
    })
  }

  ngOnInit(): void {
    this.generateYears();
  }

  generateYears(): void {
    const currentYear = moment().year();
    for (let i = 0; i < 20; i++) {
      this.years.push(currentYear - i);
    }
  }

  selectTypeVehicle(item: Category) {
    this.categorySelected = item;
    // Thay đổi loại xe thì sẽ tạo filter query mới
    const newData: VehicleParamsModel | undefined = {
      type: item.key
    }
    this.currParams = newData
    this.categorySelected = item
    // this._businessService.filterParams.next(newData)
    // this._businessService.categorySelected.next(item)
  }

  onSelectBodyStyle($event: BodyStyle) {
    if ($event.key === this.currParams?.body_style) {
      this.setBodyStyle(undefined)
    } else {
      this.setBodyStyle($event)
    }
  }

  setBodyStyle($event?: BodyStyle) {
    const curr: VehicleParamsModel | undefined = this.currParams
    const newData: VehicleParamsModel | undefined = {
      ...curr,
      body_style: $event?.key
    }
    this.currParams = newData

    // this._businessService.filterParams.next(newData)
  }

  onSelectBrand($event: Brand) {
    if ($event.name === this.currParams?.vehicle_manufacturer) {
      this.setBrand(undefined)
    } else {
      this.setBrand($event)
    }
  }

  setBrand($event?: Brand) {
    const curr: VehicleParamsModel | undefined = this.currParams
    const newData: VehicleParamsModel | undefined = {
      ...curr,
      vehicle_manufacturer: $event?.name
    }
    this.brandSelected = $event
    this.currParams = newData

    // this._businessService.filterParams.next(newData)
    // this._businessService.manufactureSelected.next($event)
  }

  onSelectModel(item: string) {
    let curr: VehicleParamsModel | undefined = this.currParams
    if (item === this.currParams?.vehicle_line) {
      curr = {
        ...curr,
        vehicle_line: undefined
      }
    } else {
      curr = {
        ...curr,
        vehicle_line: item
      }
    }
    console.log('cbr.', curr)
    this.currParams = curr

    // this._businessService.filterParams.next(curr)

  }

  onSelectFuel(item: Fuel) {
    let curr: VehicleParamsModel | undefined = this.currParams
    if (item.name === this.currParams?.fuel) {
      curr = {
        ...curr,
        fuel: undefined
      }
    } else {
      curr = {
        ...curr,
        fuel: item.name
      }
    }
    console.log('cbr.', curr)

    this.currParams = curr

    // this._businessService.filterParams.next(curr)
  }

  onSelectYear(item: number) {
    let curr: VehicleParamsModel | undefined = this.currParams
    if (item === this.currParams?.production_time) {
      curr = {
        ...curr,
        production_time: undefined
      }
    } else {
      curr = {
        ...curr,
        production_time: item
      }
    }
    this.currParams = curr
    // this._businessService.filterParams.next(curr)
  }

  onSelectProvince(item: Province) {
    const currentProvince = this.provinceSelected
    if (item.code_name === currentProvince?.code_name) {
      this.provinceSelected = {name: 'Toàn quốc'}
    } else {
      this.provinceSelected = item
    }
  }

  onSelectColor(item: ColorVehicle) {
    let curr: VehicleParamsModel | undefined = this.currParams
    if (item.name === this.currParams?.colors) {
      curr = {
        ...curr,
        colors: undefined
      }
    } else {
      curr = {
        ...curr,
        colors: item.name
      }
    }
    console.log('cbr.', curr)

    this.currParams = curr
    // this._businessService.filterParams.next(curr)
  }

  onSelectSeat(item: number) {
    let curr: VehicleParamsModel | undefined = this.currParams
    if (item === this.currParams?.number_of_seats) {
      curr = {
        ...curr,
        number_of_seats: undefined
      }
    } else {
      curr = {
        ...curr,
        number_of_seats: item
      }
    }
    console.log('cbr.', curr)

    this.currParams = curr
    // this._businessService.filterParams.next(curr)
  }

  trackByFn(index: number, item: any) {
    return index; // Use the 'id' property as the unique identifier
  }

  selectVehicleStatus(item: string) {
    let curr: VehicleParamsModel | undefined = this.currParams
    if (item === this.currParams?.vehicle_status) {
      curr = {
        ...curr,
        vehicle_status: undefined
      }
    } else {
      curr = {
        ...curr,
        vehicle_status: item
      }
    }
    this.currParams = curr
  }

  goToSecondStep() {
    const paramsNews = this._service.paramsNews.getValue()
    this.onNextStep.emit(this.currParams)
    this._service.paramsNews.next({
      ...paramsNews,
      type: this.categorySelected?.key,
      body_style: this.currParams?.body_style,
      vehicle_manufacturer: this.currParams?.vehicle_manufacturer,
      vehicle_line: this.currParams?.vehicle_line,
      fuel: this.currParams?.fuel,
      production_time: this.currParams?.production_time ? this.currParams.production_time.toString() : undefined,
      province_code: this.provinceSelected?.code_name,
      colors: this.currParams?.colors,
      vehicle_status: this.currParams?.vehicle_status,
      number_of_seats: this.currParams?.number_of_seats
    })
    window.scrollTo({top: 0, behavior: 'smooth'});
  }

  protected readonly GlobalConstants = GlobalConstants;
}
