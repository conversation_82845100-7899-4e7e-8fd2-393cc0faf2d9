.menu-container {
  //width: 100%;
  position: relative;
  background-color: var(--color-white);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding-block: var(--padding-100);
  box-sizing: border-box;
  gap: var(--gap-48);

  // Menu Content
  .menu-content {
    align-self: stretch;
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 100%;

    .menu-title {
      font-size: var(--font-size-36);
      color: var(--color-steelblue-100);
      margin: 0;
    }

    .note {
      color: var(--color-gray-100);
      margin-top: var(--padding-15);
      font-size: var(--font-size-16);
    }

    .category-buttons {
      display: flex;
      gap: var(--gap-24);

      button {
        background-color: var(--color-steelblue-300);
        color: var(--color-white);
        padding: 16px 30px;
        border: none;
        border-radius: var(--br-5);
        cursor: pointer;

        &:hover {
          background-color: var(--color-steelblue-100);
        }
        &.active {
          background-color: var(--color-steelblue-100); // Màu khi active
        }
      }
    }

    .buffet-showcase {
      display: flex;
      justify-content: space-between;
      margin-top: var(--padding-40);
      flex-wrap: wrap;
      width: 100%;
      .buffet-card {
        width: 48%;
        display: flex;
        flex-direction: column;
        align-items: start;
        text-align: start;

        .buffet-image {
          width: 100%;
          height: 287px;
          border-radius: var(--br-10);
          object-fit: cover;
          transition: all 0.3s ease;
          position: relative;

          &:hover {
            filter: brightness(50%);
          }

          &.img-error {
            opacity: 0.7;
            filter: grayscale(20%);
          }
        }

        .buffet-name {
          font-size: var(--font-size-20);
          margin: var(--gap-10) 0;
          color: var(--color-gray-100);
          font-weight: 700;
        }

        .buffet-description {
          font-size: var(--font-size-14);
          color: var(--color-gray-100);
        }

        .buffet-price {
          color: var(--color-steelblue-100);
          font-size: var(--font-size-24);
        }
        .buffet-price-person {
          color: var(--color-steelblue-100);
          font-size: var(--font-size-16);
        }
        .price-label {
          background-color: var(--color-steelblue-300);
          color: var(--color-white);
          font-size: var(--font-size-12);
          margin-right: var(--padding-12);
          padding-inline: var(--padding-10);
          border-radius: var(--br-3);
        }
      }
    }

    .reserve-btn {
      background-color: var(--color-steelblue-300);
      color: var(--color-white);
      padding: var(--padding-10) var(--padding-20);
      border: none;
      border-radius: var(--br-5);
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 500;
      min-width: 150px;

      &:hover:not(:disabled) {
        background-color: var(--color-steelblue-100);
        transform: translateY(-1px);
      }

      &:disabled,
      &.disabled {
        background-color: var(--color-darkgray-200);
        color: var(--color-darkgray-100);
        cursor: not-allowed;
        opacity: 0.6;
        transform: none;

        &:hover {
          background-color: var(--color-darkgray-200);
          transform: none;
        }
      }
    }
  }

  // Branches Section
  .branches-section {
    align-self: stretch;
    display: flex;
    flex-direction: column;
    gap: var(--gap-32);
    max-width: 100%;

    .branches-title {
      font-size: var(--font-size-24);
      color: var(--color-steelblue-100);
    }

    .loading-message {
      text-align: center;
      color: var(--color-darkgray-100);
      font-style: italic;
      padding: var(--padding-20);
    }

    .branch-list {
      display: flex;
      gap: var(--gap-24);
      flex-wrap: wrap;

      .branch-card {
        width: 48%;
        border: 2px solid var(--color-gainsboro-100);
        border-radius: var(--br-10);
        padding: var(--padding-20);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        background-color: var(--color-white);

        .branch-info {
          margin-bottom: var(--gap-16);

          .branch-name {
            display: flex;
            align-items: center;
            margin-bottom: var(--gap-12);

            .branch-icon {
              color: var(--color-steelblue-100);
              width: 16px;
              height: 16px;
              margin-right: var(--gap-8);
              flex-shrink: 0;
            }

            .branch-text {
              color: var(--color-steelblue-100);
              font-weight: 600;
              font-size: var(--font-size-16);
            }
          }

          .branch-address,
          .branch-phone {
            display: flex;
            align-items: flex-start;
            margin-bottom: var(--gap-8);
            font-size: var(--font-size-14);
            color: var(--color-darkgray-100);

            .location-icon,
            .phone-icon {
              width: 16px;
              height: 16px;
              margin-right: var(--gap-8);
              flex-shrink: 0;
              color: var(--color-darkgray-100);
            }

            span:last-child {
              line-height: 1.4;
            }
          }
        }

        .directions-btn {
          background-color: var(--color-steelblue-300);
          color: var(--color-white);
          padding: var(--padding-10) var(--padding-20);
          border: none;
          border-radius: var(--br-5);
          cursor: pointer;
          font-size: var(--font-size-14);
          font-weight: 500;
          align-self: flex-start;
          transition: background-color 0.3s ease;

          &:hover {
            background-color: var(--color-steelblue-100);
            transform: translateY(-1px);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }
  }

  .separate {
    background-color: var(--color-gainsboro-100);
    width: 100vw;
    height: 1px;
    left: -80px;
    position: relative;
  }

  // Responsive Design - DI CHUYỂN VÀO TRONG .menu-container
  @media screen and (max-width: 1200px) {
    .menu-content .buffet-showcase {
      flex-wrap: wrap;
    }
  }

  @media screen and (max-width: 750px) {
    gap: var(--gap-22);

    .menu-content {
      .menu-title {
        font-size: var(--font-size-19);
      }

      .buffet-showcase {
        .buffet-card {
          .buffet-price {
            font-size: var(--font-size-19) !important;
          }
          .buffet-price-person {
            font-size: var(--font-size-14)!important;
          }
        }
      }
    }

    .branches-section {
      gap: var(--gap-18);

      .branches-title {
        font-size: var(--font-size-19);
      }

      .branch-list {
        flex-direction: column;

        .branch-card {
          min-width: unset;
          width: 100%;
        }
      }
    }
  }

  @media screen and (max-width: 450px) {
    gap: var(--gap-48);
    padding-left: var(--padding-40);
    padding-right: var(--padding-40);
    box-sizing: border-box;

    .menu-content {
      .buffet-showcase {
        .buffet-card {
          .buffet-price {
            font-size: var(--font-size-14) !important;
          }
          .buffet-price-person {
            font-size: var(--font-size-8)!important;
          }
        }
      }
    }

    .branches-section {
      gap: var(--gap-30);

      .branch-list {
        .branch-card {
          padding: var(--padding-15) !important;
          min-width: unset !important;

          .branch-info {
            .branch-name .branch-text {
              font-size: var(--font-size-14);
            }

            .branch-address,
            .branch-phone {
              font-size: var(--font-size-12);
            }
          }
        }
      }
    }
  }
}
