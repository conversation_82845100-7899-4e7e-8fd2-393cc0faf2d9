import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { ApiService } from './api.service';

// Banner model interfaces
export interface Banner {
  _id: string;
  name: string;
  screen: string;
  params: string;
  thumbail: string;
  modifyAt: number;
  createAt: number;
  order: number;
  type: number;
  status: number;
  province: string;
}

export interface BannerResponse {
  error: boolean;
  message: string;
  data: {
    banners: Banner[];
  };
}

@Injectable({
  providedIn: 'root',
})
export class BannerService {
  private bannersSubject = new BehaviorSubject<Banner[]>([]);
  public banners: Observable<Banner[]> = this.bannersSubject.asObservable();

  constructor(private apiService: ApiService) {}

  /**
   * L<PERSON>y danh sách banner
   * @returns Observable<BannerResponse>
   */
  public getBanners(): Observable<BannerResponse> {
    return this.apiService.get<any>('user/api/get-banner.html').pipe(
      map(response => {
        console.log('Get banners response:', response);

        // API trả về cấu trúc {error, message, data: {banners: []}}
        const bannerResponse: BannerResponse = {
          error: response.error || false,
          message: response.message || '',
          data: {
            banners: response.data?.banners || [],
          },
        };

        // Nếu lấy danh sách thành công, cập nhật BehaviorSubject
        if (!bannerResponse.error && bannerResponse.data && bannerResponse.data.banners) {
          this.bannersSubject.next(bannerResponse.data.banners);
        }

        return bannerResponse;
      }),
      catchError(error => {
        console.error('Error fetching banners:', error);
        throw error;
      }),
    );
  }

  /**
   * Lấy danh sách banner cho cửa hàng theo tag
   * @param tag Tag để lọc banner (ví dụ: 'cho_hai_san')
   * @returns Observable<BannerResponse>
   */
  public getBannerStores(tag: string): Observable<BannerResponse> {
    return this.apiService.get<any>(`user/api/get-banner-store.html?tag=${tag}`).pipe(
      map(response => {
        console.log('Get banner stores response:', response);

        // API trả về cấu trúc {error, message, data: {banners: []}}
        const bannerResponse: BannerResponse = {
          error: response.error || false,
          message: response.message || '',
          data: {
            banners: response.data?.banners || [],
          },
        };

        return bannerResponse;
      }),
      catchError(error => {
        console.error('Error fetching banner stores:', error);
        throw error;
      }),
    );
  }

  /**
   * Lấy banner theo tỉnh/thành phố
   * @param province Mã tỉnh/thành phố, ví dụ: "toan-quoc", "thanh_pho_hai_phong"
   * @returns Observable<Banner[]> Danh sách banner đã lọc
   */
  public getBannersByProvince(province: string): Observable<Banner[]> {
    return this.banners.pipe(
      map(banners => banners.filter(banner => banner.province === province)),
    );
  }

  /**
   * Xóa dữ liệu banner khỏi BehaviorSubject
   */
  public clearBanners(): void {
    this.bannersSubject.next([]);
  }
}
