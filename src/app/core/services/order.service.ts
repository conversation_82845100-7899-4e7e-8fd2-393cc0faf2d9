import { Injectable } from '@angular/core';
import { Observable, firstValueFrom } from 'rxjs';
import { ApiService } from './api.service';
import { AuthService } from './auth.service';
import { OrderData, CartItem, BRAND_ID } from '../models/cart.model';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class OrderService {
  constructor(private apiService: ApiService, private authService: AuthService) {}

  /**
   * Create order
   */
  createOrder(orderData: OrderData): Observable<any> {
    return this.apiService.post('user/api/create-order-product', orderData, {
      params: { output: 'json' },
    });
  }

  /**
   * Check coupon validity
   */
  checkCoupon(
    code: string,
    totalPrice: number,
    storeId: string = '',
    userId: string = '',
  ): Observable<any> {
    const body = {
      code,
      subTotal: totalPrice,
      storeId: environment.storeId,
      userId,
    };
    return this.apiService.post('user/api/calculate-order', body, { params: { output: 'json' } });
  }

  /**
   * Get branch stores
   */
  getBranchStores(brandId: string = BRAND_ID): Observable<any> {
    return this.apiService.get(`user/api/services-of-brand/${brandId}/table`, {
      output: 'json',
    });
  }

  /**
   * Generate order ID
   */
  generateOrderId(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    return `ORD${timestamp}${random}`;
  }

  /**
   * Prepare order data for cash payment
   */
  prepareOrderData(
    cartItems: CartItem[],
    customerInfo: any,
    storeInfo: any,
    couponCode?: string,
    totalAmount?: number,
  ): OrderData {
    const user = this.authService.currentUserValue;

    return {
      orderId: this.generateOrderId(),
      userId: user?._id || '',
      storeId: storeInfo._id,
      products: cartItems.map(item => ({
        productId: item.productId,
        count: item.count,
        shopId: item.shopId,
        classifyActive: item.classifyActive,
        noteProduct: item.noteProduct,
        info: item.info,
      })),
      name: customerInfo.name || user?.fullName || '',
      phone: customerInfo.phone,
      province: customerInfo.province || '',
      district: customerInfo.district || '',
      ward: customerInfo.ward || '',
      street: customerInfo.street || '',
      location: customerInfo.address,
      payment: 1, // Cash payment
      bankCode: 'TM', // Tiền mặt
      couponCode: couponCode || '',
      totalAmount: totalAmount || 0,
      transportFee: 0,
      shippingService: 'ghtk',
    };
  }

  /**
   * Prepare order data for bank payment
   */
  prepareOrderDataForBank(
    cartItems: CartItem[],
    customerInfo: any,
    storeInfo: any,
    bankCode: string,
    couponCode?: string,
    totalAmount?: number,
  ): OrderData {
    const user = this.authService.currentUserValue;

    return {
      orderId: this.generateOrderId(),
      userId: user?._id || '',
      storeId: storeInfo._id,
      products: cartItems.map(item => ({
        productId: item.productId,
        count: item.count,
        shopId: item.shopId,
        classifyActive: item.classifyActive,
        noteProduct: item.noteProduct,
        info: item.info,
      })),
      name: customerInfo.name || user?.fullName || '',
      phone: customerInfo.phone,
      province: customerInfo.province || '',
      district: customerInfo.district || '',
      ward: customerInfo.ward || '',
      street: customerInfo.street || '',
      location: customerInfo.address,
      payment: 0, // Online payment
      bankCode: bankCode,
      couponCode: couponCode || '',
      totalAmount: totalAmount || 0,
      transportFee: 0,
      shippingService: 'ghtk',
    };
  }

  /**
   * Process cash payment
   */
  async processCashPayment(orderData: OrderData): Promise<any> {
    try {
      const response = await firstValueFrom(this.createOrder(orderData));

      if (response && !response.error) {
        return {
          success: true,
          orderId: orderData.orderId,
          message: 'Đặt hàng thành công',
        };
      } else {
        return {
          success: false,
          message: response?.message || 'Có lỗi xảy ra khi đặt hàng',
        };
      }
    } catch (error) {
      console.error('Error processing cash payment:', error);
      return {
        success: false,
        message: 'Có lỗi xảy ra khi đặt hàng',
      };
    }
  }

  /**
   * Process bank payment
   */
  async processBankPayment(orderData: OrderData): Promise<any> {
    try {
      const response = await firstValueFrom(this.createOrder(orderData));

      if (response && !response.error && response.data?.vnpUrl) {
        return {
          success: true,
          orderId: orderData.orderId,
          vnpUrl: response.data.vnpUrl,
          paymentId: response.data.paymentId,
          message: 'Chuyển hướng đến trang thanh toán',
        };
      } else {
        return {
          success: false,
          message: response?.message || 'Có lỗi xảy ra khi tạo link thanh toán',
        };
      }
    } catch (error) {
      console.error('Error processing bank payment:', error);
      return {
        success: false,
        message: 'Có lỗi xảy ra khi tạo link thanh toán',
      };
    }
  }

  /**
   * Calculate total price with discount
   */
  calculateTotalPrice(cartItems: CartItem[], discountAmount: number = 0): number {
    const subtotal = cartItems.reduce((total, item) => {
      if (!item.selected) return total;

      const price = item.classifyActive?.price
        ? parseInt(item.classifyActive.price.replace(/,/g, ''))
        : item.info?.price || 0;

      return total + price * item.count;
    }, 0);

    return Math.max(0, subtotal - discountAmount);
  }

  /**
   * Validate order data
   */
  validateOrderData(orderData: OrderData): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!orderData.userId) {
      errors.push('Thông tin người dùng không hợp lệ');
    }

    if (!orderData.storeId) {
      errors.push('Vui lòng chọn cửa hàng');
    }

    if (!orderData.products || orderData.products.length === 0) {
      errors.push('Giỏ hàng trống');
    }

    if (!orderData.name) {
      errors.push('Vui lòng nhập họ và tên');
    }

    if (!orderData.phone) {
      errors.push('Vui lòng nhập số điện thoại');
    }

    if (!orderData.location) {
      errors.push('Vui lòng nhập địa chỉ giao hàng');
    }

    if (orderData.payment === 0 && !orderData.bankCode) {
      errors.push('Vui lòng chọn ngân hàng thanh toán');
    }

    if (orderData.totalAmount <= 0) {
      errors.push('Tổng tiền không hợp lệ');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Format price to VND
   */
  formatPrice(price: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price);
  }

  /**
   * Format price without currency symbol
   */
  formatPriceNumber(price: number): string {
    return new Intl.NumberFormat('vi-VN').format(price);
  }
}
