import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { ApiService } from './api.service';

export interface HoTroData {
  trungTamHoTro: string;
  chinhSachBaoMat: string;
  dieuKhoanDichVu: string;
  gioiThieu: string;
}

export interface HoTroResponse {
  error: boolean;
  message: string;
  data: HoTroData | null;
}

@Injectable({
  providedIn: 'root'
})
export class HoTroService {

  constructor(private apiService: ApiService) {}

  /**
   * Lấy dữ liệu hỗ trợ từ API
   * @returns Observable<HoTroResponse>
   */
  public getHoTroData(): Observable<HoTroResponse> {
    return this.apiService.get<any>('user/api/ho-tro.html')
      .pipe(
        map(response => {
          console.log('Get ho-tro data response:', response);
          
          // API trả về cấu trúc {error, message, data}
          const hoTroResponse: HoTroResponse = {
            error: response.error || false,
            message: response.message || '',
            data: response.data || null
          };
          
          return hoTroResponse;
        }),
        catchError(error => {
          console.error('Error fetching ho-tro data:', error);
          throw error;
        })
      );
  }

  /**
   * Lấy nội dung của một trang hỗ trợ cụ thể
   * @param pageName Tên trang (trung-tam-ho-tro, chinh-sach-bao-mat, etc.)
   * @returns Observable<string>
   */
  public getPageContent(pageName: string): Observable<string> {
    const fieldMapping: { [key: string]: string } = {
      'trung-tam-ho-tro': 'trungTamHoTro',
      'chinh-sach-bao-mat': 'chinhSachBaoMat',
      'dieu-khoan-dich-vu': 'dieuKhoanDichVu',
      'gioi-thieu': 'gioiThieu'
    };

    const fieldName = fieldMapping[pageName];
    if (!fieldName) {
      throw new Error(`Invalid page name: ${pageName}`);
    }

    return this.getHoTroData().pipe(
      map(response => {
        if (!response.error && response.data && response.data[fieldName as keyof HoTroData]) {
          return response.data[fieldName as keyof HoTroData];
        }
        return '';
      })
    );
  }
}