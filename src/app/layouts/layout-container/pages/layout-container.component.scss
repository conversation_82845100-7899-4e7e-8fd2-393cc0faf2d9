.layout-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh; /* Đảm bảo container có chiều cao tối thiểu bằng viewport */
  position: relative;
}

.main-content {
  flex: 1 0 auto; /* <PERSON> phép main-content mở rộng để đ<PERSON>y footer xuống dưới */
  width: 100%;
  padding-top: 45px
}

//.content-layout {
//  padding-top: 25vh; /* Tự động thêm khoảng trống */
//}

footer {
  background-color: #f8f9fa;
  margin-top: auto;
  flex-direction: row;
  display: flex;
  height: 380px;
}

.flex-container {
  flex-direction: column;
  display: flex;
  align-items: start;
  color: black;
  font-weight: 600;
}

.logo {
  width: 130px;
  height: 29px;
  margin-bottom: 28px;
}

a {
  color: black;
  text-decoration: none;
  font-weight: 400;
}

.info {
  text-align: left;
  margin-right: 60px;
}

.icon-social {
  margin: 10px 20px 10px 0;
}

.content {
  display: flex;
  min-height: 900px;
  width: 100%;
  //padding-inline: 70px;
  //align-items: center;
  flex-direction: column;
}

.logo {
  height: 30px;
  width: 130px;
  min-width: 45px;
  min-height: 10px;
}

.header-top {
  flex: 1;
  display: flex;
  align-items: center;
  padding-inline: 6%;
}

.header-under {
  flex: 1;
  //height: 50%;
  display: flex;
}

.search-bar {
  height: 110px;
}

.header-top-right {
  display: flex;
  align-items: center;
}

.header-under-left {
  display: flex;
  flex: 2;
  align-items: center;
}

.header-under-right {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: end;
}

.post-button {
  display: flex;
  align-items: center;
  background-color: #d32f2f; /* Màu đỏ */
  color: #fff;
  border: none;
  border-radius: 20px;
  height: 40px;
  padding-inline: 20px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-right: 10px;
  min-width: 153px;

  &:hover {
    background-color: #b71c1c; /* Màu đỏ đậm hơn */
  }
}

.user-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #ccc; /* Viền */
  border-radius: 50px;
  height: 40px;
  padding: 10px 5px 10px 10px;
  cursor: pointer;
  transition: box-shadow 0.3s;
  background-color: #DADADA;
  width: 80px;

  &:hover {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* Hiệu ứng đổ bóng */
  }
}

.mark-province,
.hearth {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-left: 40px;
  border-radius: 5px;
  padding: 5px;
  cursor: pointer;
  border: 0;
  background: transparent;

  mat-icon {
    color: #9D9D9D;
  }

  &.active mat-icon {
    color: #d32f2f;
  }
}

.icon-header {
  width: 24px !important;
  min-width: 24px !important;
  height: 24px !important;
}

.line {
  width: 100%;
  height: 1px;
  background-color: #DADADA;
  margin-top: 24px;
}

.read-more {
  display: flex;
  align-items: center;
  align-self: center;
  background-color: rgba(31, 31, 31, 0.75);
  color: white;
  border: none;
  border-radius: 50px;
  height: 40px;
  padding-inline: 20px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;

  &:hover {
    background-color: #151515FF;
  }
}

.no-data {
  margin-top: 100px;
  text-align: center;
}

.mat-menu-content {
  background-color: #e0f7fa;
  padding: 12px;
}

.menu-province .mat-mdc-menu-content {
  height: 300px;
}
