import {Component, OnInit, ViewEncapsulation} from '@angular/core';
import {TypeVehicleComponent} from "../type-vehicle/type-vehicle.component";
import {MatIcon} from "@angular/material/icon";
import {ManufacturerComponent} from "../manufacturer/manufacturer.component";
import {JsonPipe, NgClass, NgForOf, NgIf} from "@angular/common";

import {CarouselModule, OwlOptions} from "ngx-owl-carousel-o";
import {MatTooltip} from "@angular/material/tooltip";
import {MatButton, MatIconButton} from "@angular/material/button";
import moment from "moment";
import {ExpansionContainerComponent} from "../expansion-container/expansion-container.component";
import {TruncatePipe} from "../../../../pipe/truncate.pipe";
import {
  BodyStyle,
  Brand,
  Category,
  ColorVehicle,
  FilterConfig,
  Fuel,
  Price,
  Province,
  VehicleParamsModel
} from "../../model";
import {LayoutBusinessLogicService} from "../../service/layout-business.service";
import {MatDialogClose, MatDialogRef} from "@angular/material/dialog";
import {GlobalConstants} from "../../../../constants";

@Component({
  selector: 'app-filter-kit',
  standalone: true,
  imports: [
    ExpansionContainerComponent,
    TypeVehicleComponent,
    MatIcon,
    ManufacturerComponent,
    NgClass,
    NgForOf,
    CarouselModule,
    TruncatePipe,
    MatTooltip,
    MatIconButton,
    NgIf,
    JsonPipe,
    MatButton,
    MatDialogClose,
  ],
  templateUrl: './filter-kit.component.html',
  styleUrl: './filter-kit.component.scss',
  encapsulation: ViewEncapsulation.None
})
export class FilterKitComponent implements OnInit {
  // @Input() menu: MatMenu
  configFilter?: FilterConfig
  categorySelected?: Category

  province: Province[] = []
  provinceSelected?: Province

  currParams?: VehicleParamsModel

  years: number[] = [];

  brandSelected?: Brand

  customOptions: OwlOptions = {
    margin: 10,
    autoWidth: true,
    nav: false,
    loop: true,
    rewind: true,
    dots: false,
    autoplay: true,
    autoplayTimeout: 3000,
    autoplayHoverPause: true,
    responsive: {
      0: {
        items: 3
      },
      400: {
        items: 5
      },
      740: {
        items: 7
      },
      940: {
        items: 7
      }
    },
  };

  constructor(private _businessService: LayoutBusinessLogicService, public dialogRef: MatDialogRef<FilterKitComponent>) {

    this._businessService.categorySelected$.subscribe(e => {
      this.categorySelected = e
    })
    this._businessService.manufactureSelected$.subscribe(e => {
      this.brandSelected = e
    })
    this._businessService.filterParams$.subscribe(e => {
      this.currParams = e
    })
    this._businessService.province$.subscribe(e => {
      this.province = e ?? []
    })
    this._businessService.provinceSelected$.subscribe(e => {
      this.provinceSelected = e
    })
    this._businessService.filterConfig$.subscribe(e => {
      this.configFilter = e
    })
  }

  ngOnInit(): void {
    this.generateYears();
  }

  generateYears(): void {
    const currentYear = moment().year();
    for (let i = 0; i < 20; i++) {
      this.years.push(currentYear - i);
    }
  }

  selectTypeVehicle(item: Category) {
    this.categorySelected = item;
    // Thay đổi loại xe thì sẽ tạo filter query mới
    const newData: VehicleParamsModel | undefined = {
      type: item.key
    }
    this.currParams = newData
    this.categorySelected = item
    // this._businessService.filterParams.next(newData)
    // this._businessService.categorySelected.next(item)
  }

  onSelectBodyStyle($event: BodyStyle) {
    if ($event.key === this.currParams?.body_style) {
      this.setBodyStyle(undefined)
    } else {
      this.setBodyStyle($event)
    }
  }

  setBodyStyle($event?: BodyStyle) {
    const curr: VehicleParamsModel | undefined = this.currParams
    const newData: VehicleParamsModel | undefined = {
      ...curr,
      body_style: $event?.key
    }
    this.currParams = newData

    // this._businessService.filterParams.next(newData)
  }

  onSelectBrand($event: Brand) {
    if ($event.name === this.currParams?.vehicle_manufacturer) {
      this.setBrand(undefined)
    } else {
      this.setBrand($event)
    }
  }

  setBrand($event?: Brand) {
    const curr: VehicleParamsModel | undefined = this.currParams
    const newData: VehicleParamsModel | undefined = {
      ...curr,
      vehicle_manufacturer: $event?.name
    }
    this.brandSelected = $event
    this.currParams = newData

    // this._businessService.filterParams.next(newData)
    // this._businessService.manufactureSelected.next($event)
  }

  onSelectModel(item: string) {
    let curr: VehicleParamsModel | undefined = this.currParams
    if (item === this.currParams?.vehicle_line) {
      curr = {
        ...curr,
        vehicle_line: undefined
      }
    } else {
      curr = {
        ...curr,
        vehicle_line: item
      }
    }
    this.currParams = curr
  }

  onSelectFuel(item: Fuel) {
    let curr: VehicleParamsModel | undefined = this.currParams
    if (item.name === this.currParams?.fuel) {
      curr = {
        ...curr,
        fuel: undefined
      }
    } else {
      curr = {
        ...curr,
        fuel: item.name
      }
    }
    this.currParams = curr
  }

  onSelectPrice(item: Price) {
    let curr: VehicleParamsModel | undefined = this.currParams
    if (item.min === this.currParams?.price_min && item.max === this.currParams?.price_max) {
      curr = {
        ...curr,
        price_min: undefined,
        price_max: undefined
      }
    } else {
      curr = {
        ...curr,
        price_min: item.min === 0 ? 0 : item.min ? item.min * 1000000 : undefined,
        price_max: item.max ? item.max * 1000000 : undefined
      }
    }
    this.currParams = curr
  }

  activeClassPrice(item: Price): string {
    if (this.currParams?.price_min != null && item.min === this.currParams.price_min / 1000000) {
      return 'active-filter'
    }
    return ''
  }

  onSelectYear(item: number) {
    let curr: VehicleParamsModel | undefined = this.currParams
    if (item === this.currParams?.production_time) {
      curr = {
        ...curr,
        production_time: undefined
      }
    } else {
      curr = {
        ...curr,
        production_time: item
      }
    }
    this.currParams = curr
  }

  onSelectProvince(item: Province) {
    const currentProvince = this.provinceSelected
    if (item.code_name === currentProvince?.code_name) {
      this.provinceSelected = {name: 'Toàn quốc'}
    } else {
      this.provinceSelected = item
    }
  }

  onSelectColor(item: ColorVehicle) {
    let curr: VehicleParamsModel | undefined = this.currParams
    if (item.name === this.currParams?.colors) {
      curr = {
        ...curr,
        colors: undefined
      }
    } else {
      curr = {
        ...curr,
        colors: item.name
      }
    }
    this.currParams = curr
  }

  trackByFn(index: number, item: any) {
    return index; // Use the 'id' property as the unique identifier
  }

  clearFilter() {
    this._businessService.clearFilter()
  }

  applyFilter() {
    this._businessService.filterParams.next(this.currParams)
    if (this._businessService.provinceSelected.getValue() !== this.provinceSelected) {
      this._businessService.provinceSelected.next(this.provinceSelected)
    }
    this._businessService.categorySelected.next(this.categorySelected)
    this._businessService.manufactureSelected.next(this.brandSelected)
    this._businessService.navigateFilter()
    // this.menu.closed.emit()
    this.dialogRef.close()
  }

  closePopup() {

  }

  selectVehicleStatus(item: string) {
    let curr: VehicleParamsModel | undefined = this.currParams
    if (item === this.currParams?.vehicle_status) {
      curr = {
        ...curr,
        vehicle_status: undefined
      }
    } else {
      curr = {
        ...curr,
        vehicle_status: item
      }
    }
    this.currParams = curr
  }

  protected readonly GlobalConstants = GlobalConstants;
}
