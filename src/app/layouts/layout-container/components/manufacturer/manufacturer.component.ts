import {Component, Input} from '@angular/core';
import {<PERSON>son<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ForO<PERSON>, NgIf} from "@angular/common";
import {MatIcon} from "@angular/material/icon";
import {MatIconButton} from "@angular/material/button";
import {TruncatePipe} from "../../../../pipe/truncate.pipe";
import {MatTooltip} from "@angular/material/tooltip";
import {Brand, VehicleParamsModel} from "../../model";
import {CarouselModule, OwlOptions} from "ngx-owl-carousel-o";
import {LayoutBusinessLogicService} from "../../service/layout-business.service";
import {Router} from "@angular/router";
import {GlobalConstants} from "../../../../constants";

@Component({
  selector: 'app-manufacturer',
  standalone: true,
  imports: [
    NgForOf,
    MatIcon,
    MatIconButton,
    TruncatePipe,
    NgIf,
    MatTooltip,
    NgClass,
    CarouselModule,
    Json<PERSON>ip<PERSON>,
  ],
  templateUrl: './manufacturer.component.html',
  styleUrl: './manufacturer.component.scss'
})
export class ManufacturerComponent {
  listBrand?: Brand[] = []
  brandSelected?: string
  showCarousel: boolean = true; // Dùng để reset carousel nếu không nó sẽ bị trùng lặp dữ liệu

  @Input() optionalActiveStyle = ''
  @Input() customOptions: OwlOptions = {
    margin: 10,
    autoWidth: true,
    loop: true,
    rewind: true,
    nav: false,
    dots: false,
    autoplay: true,
    autoplayTimeout: 3000,
    autoplayHoverPause: true,
    responsive: {
      0: {items: 6},        // Số item hiển thị trên màn hình nhỏ
      600: {items: 13},      // Số item hiển thị trên màn hình trung bình
      1400: {items: 20}      // Số item hiển thị trên màn hình lớn
    },
  };

  constructor(private router: Router, private _businessService: LayoutBusinessLogicService) {

    this._businessService.manufactureSelected$.subscribe(e => {
      this.brandSelected = e?.name
    })
    this._businessService.categorySelected$.subscribe(e => {
      this.listBrand = e?.brands
      this.showCarousel = false
      setTimeout(() => {
        this.showCarousel = true
      }, 0)
    })
  }

  onSelect(item?: Brand) {
    this._businessService.navigateFilter(item, () => {
      this.setBrand(item)
    })
  }

  setBrand(item?: Brand) {
    if (item?.name === this.brandSelected) {
      //Nếu trước đó đã chọn hãng này rồi thì sẽ chuyển thành không chọn
      this.brandSelected = undefined
      this.setParams(undefined)
    } else {
      this.brandSelected = item?.name
      this.setParams(item)
    }
  }

  setParams(item?: Brand) {
    const curr: VehicleParamsModel | undefined = this._businessService.filterParams.getValue()
    const newData: VehicleParamsModel | undefined = {
      ...curr,
      vehicle_manufacturer: item?.name
    }
    this._businessService.filterParams.next(newData)
    this._businessService.manufactureSelected.next(item)
  }

  trackByFn(index: number, item: any) {
    return index; // Use the 'id' property as the unique identifier
  }

  protected readonly GlobalConstants = GlobalConstants;
}
