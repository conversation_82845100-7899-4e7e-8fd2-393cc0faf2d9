@import "../variables";

.mat-mdc-raised-button.mat-primary,
.mat-mdc-raised-button.mat-accent,
.mat-mdc-raised-button.mat-warn,
.mat-mdc-unelevated-button.mat-primary,
.mat-mdc-unelevated-button.mat-accent,
.mat-mdc-unelevated-button.mat-warn,
.mdc-fab.mat-primary,
.mdc-fab.mat-accent,
.mdc-fab.mat-warn {
  color: white;
}

// Override Material primary button using MDC CSS custom properties
.mat-mdc-raised-button.mat-primary {
  --mdc-protected-button-container-color: #005b94 !important;
  --mdc-protected-button-label-text-color: white !important;
  --mat-protected-button-state-layer-color: white !important;
  --mat-protected-button-ripple-color: rgba(255, 255, 255, 0.1) !important;
  --mdc-protected-button-hover-container-color: #004a7a !important;
  --mdc-protected-button-focus-container-color: #004a7a !important;
  --mdc-protected-button-pressed-container-color: #003d66 !important;
  --mdc-protected-button-disabled-container-color: #cccccc !important;
  --mdc-protected-button-disabled-label-text-color: #666666 !important;
}

.mat-mdc-unelevated-button.mat-primary {
  --mdc-filled-button-container-color: #005b94 !important;
  --mdc-filled-button-label-text-color: white !important;
  --mat-filled-button-state-layer-color: white !important;
  --mat-filled-button-ripple-color: rgba(255, 255, 255, 0.1) !important;
  --mdc-filled-button-hover-container-color: #004a7a !important;
  --mdc-filled-button-focus-container-color: #004a7a !important;
  --mdc-filled-button-pressed-container-color: #003d66 !important;
  --mdc-filled-button-disabled-container-color: #cccccc !important;
  --mdc-filled-button-disabled-label-text-color: #666666 !important;
}

.mat-mdc-outlined-button.mat-primary {
  --mdc-outlined-button-outline-color: #005b94 !important;
  --mdc-outlined-button-label-text-color: #005b94 !important;
  --mat-outlined-button-state-layer-color: #005b94 !important;
  --mat-outlined-button-ripple-color: rgba(0, 91, 148, 0.1) !important;
  --mdc-outlined-button-hover-outline-color: #004a7a !important;
  --mdc-outlined-button-hover-label-text-color: #004a7a !important;
  --mdc-outlined-button-focus-outline-color: #004a7a !important;
  --mdc-outlined-button-focus-label-text-color: #004a7a !important;
  --mdc-outlined-button-pressed-outline-color: #003d66 !important;
  --mdc-outlined-button-pressed-label-text-color: #003d66 !important;
  --mdc-outlined-button-disabled-outline-color: #cccccc !important;
  --mdc-outlined-button-disabled-label-text-color: #666666 !important;
}

.mat-mdc-button.mat-primary {
  --mdc-text-button-label-text-color: #005b94 !important;
  --mat-text-button-state-layer-color: #005b94 !important;
  --mat-text-button-ripple-color: rgba(0, 91, 148, 0.1) !important;
  --mdc-text-button-hover-label-text-color: #004a7a !important;
  --mdc-text-button-focus-label-text-color: #004a7a !important;
  --mdc-text-button-pressed-label-text-color: #003d66 !important;
  --mdc-text-button-disabled-label-text-color: #666666 !important;
}

.mat-mdc-fab.mat-primary,
.mat-mdc-mini-fab.mat-primary {
  --mdc-fab-container-color: #005b94 !important;
  --mdc-fab-label-text-color: white !important;
  --mat-fab-state-layer-color: white !important;
  --mat-fab-ripple-color: rgba(255, 255, 255, 0.1) !important;
  --mdc-fab-hover-container-color: #004a7a !important;
  --mdc-fab-focus-container-color: #004a7a !important;
  --mdc-fab-pressed-container-color: #003d66 !important;
  --mdc-fab-disabled-container-color: #cccccc !important;
  --mdc-fab-disabled-label-text-color: #666666 !important;
}

// Fallback for older Material versions or edge cases
.mat-mdc-raised-button.mat-primary:not(:disabled),
.mat-mdc-unelevated-button.mat-primary:not(:disabled),
.mat-mdc-fab.mat-primary:not(:disabled),
.mat-mdc-mini-fab.mat-primary:not(:disabled) {
  background-color: #005b94;
  color: white;
}

.mat-mdc-outlined-button.mat-primary:not(:disabled) {
  border-color: #005b94;
  color: #005b94;
}

.mat-mdc-button.mat-primary:not(:disabled) {
  color: #005b94;
}

.mat-mdc-raised-button {
  box-shadow: $cardshadow;
}

.mat-mdc-fab,
.mat-mdc-mini-fab,
.mat-mdc-fab:hover,
.mat-mdc-fab:focus,
.mat-mdc-mini-fab:hover,
.mat-mdc-mini-fab:focus {
  box-shadow: none;
}

.mat-button-toggle-button {
  font-size: 14px;
}

@media (min-width: 768px) {
  .flex-sm-row {
    flex-direction: row !important;
  }
}


//.flex-column {
//  flex-direction: column !important;
//}

.mat-mdc-mini-fab:hover .mat-mdc-button-persistent-ripple::before {
  opacity: 0.2 !important;
}

.mat-mdc-mini-fab.mat-inherit {
  background-color: transparent !important;
}

.ngx-mat-file-input {
  .button-browse {
    height: 20px;
    width: 20px;
    padding: 0;
  }
}
