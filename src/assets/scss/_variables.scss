@import url("https://fonts.googleapis.com/css2?family=Red+Hat+Display:ital,wght@0,300..900;1,300..900&display=swap");

// font
$font-family: "Red Hat Display", sans-serif;

// light color variable
$sidebarbg: white;
$background: white;
$cardbg: white;
$toolbar: white;
$cardshadow: 0px 2px 4px -1px rgba(175, 182, 201, 0.2);
$hoverBgColor: #f6f9fc;

// Dark color variable
$darksidebarbg: #1a2537;
$darkbackground: #1f2a3d;
$darkcardbg: #1a2537;
$darktoolbar: #1a2537;
$darkcardshadow: rgba(145, 158, 171, 0.3) 0px 0px 2px 0px,
rgba(145, 158, 171, 0.02) 0px 12px 24px -4px;
$darkborderColor: #333f55;
$darkformborderColor: #465670;
$darkhoverbgcolor: #333f55;
$darkthemelightprimary: #253662;
$darkthemelightaccent: #1c455d;
$darkthemelightwarning: #4d3a2a;
$darkthemelighterror: #4b313d;
$darkthemelightsuccess: #1b3c48;
$darktextPrimary: rgba(255, 255, 255, 0.85);
$darktextBody: rgba(255, 255, 255, 0.6);

// Sidenav
$sidenav-desktop: 260px !default;
$sidenav-mini: 80px !default;
$header-height: 300px !default;

//BorderColor
$borderColor: #e0e6eb;
$borderformColor: #e0e6eb;
$bordertableColor: #111c2d0d;

// custom
$primary: #005b94;
$accent: #16cdc7;
$warning: #f8c20a;
$error: #ff6692;
$success: #36c76c;
$white: #ffffff;
$dark: #29343d;
$dark-200: #0a2540;

$light: #f2f6fa;
$muted: #526b7a;

$gray-100: #7c8fac;
$gray-200: #f6f7f9;
$bordercheckboxColor: #bdc3cd;

$light-primary: #dddbff;
$light-accent: #a6f7f5;
$light-warning: #fff9e5;
$light-error: #ffccdb;
$light-success: #ebfaf0;

// layout
$boxedWidth: 1200px;

$border-radius: 12px;

// text color
$textPrimary: #29343d;

$bodyColor: #526b7a;

$body-bg: #f4f7fb;

html {
  --mat-expansion-container-shape: 12px !important;
  --mat-form-field-container-height: 45px !important;
  --mdc-elevated-card-container-shape: 12px !important;
  --mdc-elevated-card-container-shape: 12px !important;
  --mat-sidenav-container-divider-color: #e0e6eb !important;
  --mat-standard-button-toggle-selected-state-background-color: #f2f6fa !important;
  --mat-standard-button-toggle-selected-state-text-color: #526b7a !important;
  --mdc-switch-unselected-handle-color: #29343d !important;
  --mdc-switch-unselected-hover-handle-color: #29343d !important;
  --mdc-switch-unselected-focus-handle-color: #29343d !important;
  --mdc-switch-unselected-track-color: #bdc3cd !important;
  --mdc-switch-unselected-hover-state-layer-color: #f2f6fa !important;
  --mat-stepper-header-icon-background-color: #7c8fac !important;
  --mat-stepper-header-hover-state-layer-color: #f2f6fa !important;
  --mat-expansion-header-hover-state-layer-color: #f2f6fa !important;
  --mat-expansion-header-focus-state-layer-color: #f2f6fa !important;
  --mat-icon-button-state-layer-color: #635bff !important;
  --mat-icon-button-hover-state-layer-opacity: 0.09 !important;
  --mdc-snackbar-container-color: #29343d !important;
  --mdc-outlined-button-disabled-outline-color: rgb(226 236 249) !important;
  --mdc-outlined-button-disabled-label-text-color: #7c8fac !important;
  --mat-text-button-disabled-container-color: rgb(226 236 249) !important;
  --mdc-filled-button-disabled-container-color: rgb(226 236 249) !important;
  --mdc-protected-button-disabled-container-color: rgb(226 236 249) !important;
  --mat-fab-disabled-state-container-color: rgb(226 236 249) !important;
  --mdc-list-list-item-leading-icon-color: #526b7a !important;
  --mdc-list-list-item-hover-leading-icon-color: #526b7a !important;
  .mat-mdc-tooltip .mdc-tooltip__surface {
    --mdc-plain-tooltip-container-color: #29343d !important;
  }

  .mat-mdc-button .mat-mdc-button-persistent-ripple::before {
    --mat-text-button-state-layer-color: #f2f6fa !important;
  }

  .mat-mdc-standard-chip {
    --mdc-chip-elevated-container-color: #f2f6fa !important;
    // --mdc-chip-elevated-selected-container-color: #f2f6fa !important;
    --mdc-chip-elevated-disabled-container-color: #f2f6fa !important;
    --mdc-chip-flat-disabled-selected-container-color: #f2f6fa !important;
  }

  .mat-mdc-form-field-infix {
    --mat-form-field-container-height: 45px;
    --mat-form-field-container-vertical-padding: 13px;
  }

  .mat-mdc-tab-header {
    --mat-tab-header-inactive-label-text-color: #29343d;
    --mat-tab-header-label-text-weight: 500;
  }

  .mat-mdc-radio-button .mdc-radio .mdc-radio__background::before {
    --mat-radio-ripple-color: #f2f6fa !important;
  }

  .mdc-list-item:focus::before {
    --mdc-list-list-item-focus-state-layer-color: #f2f6fa !important;
  }
}

:root {
  --mat-divider-color: #e0e6eb;
}

.mat-mdc-nav-list .mat-mdc-list-item {
  --mat-list-active-indicator-shape: 12px;
}

.mat-mdc-tooltip {
  --mdc-plain-tooltip-container-shape: 12px;
}

.mat-mdc-card {
  --mdc-elevated-card-container-shape: 12px;
}

.mdc-checkbox:hover .mdc-checkbox__ripple {
  --mdc-checkbox-unselected-hover-state-layer-opacity: 1;
  --mdc-checkbox-unselected-hover-state-layer-color: #f2f6fa;
}

.mdc-button {
  --mdc-filled-button-container-shape: 8px;
  --mdc-text-button-container-shape: 8px;
  --mdc-protected-button-container-shape: 8px;
  --mdc-outlined-button-container-shape: 8px;
  --mat-standard-button-toggle-shape: 8px;
}

.mdc-text-field {
  --mdc-outlined-text-field-container-shape: 8px;
  --mdc-outlined-text-field-outline-color: #e0e6eb;
  --mdc-outlined-text-field-input-text-color: #29343d;
}

.mat-mdc-menu-panel {
  --mat-menu-container-shape: 12px;
}

.mdc-list-item:hover::before {
  --mdc-list-list-item-hover-state-layer-opacity: 0.09;
}

.mat-mdc-mini-fab {
  --mat-fab-small-foreground-color: #29343d;
}
